[project]
name = "quivr-whisper"
version = "0.1.0"
description = "Add your description here"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
dependencies = [
    "quivr-core @ file:///${PROJECT_ROOT}/../../core",
    "flask[async]>=3.1.0",
    "openai>=1.54.5",
    "flask-caching>=2.3.0",
]
readme = "README.md"
requires-python = ">= 3.11"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
virtual = true
dev-dependencies = []

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["src/quivr_whisper"]
