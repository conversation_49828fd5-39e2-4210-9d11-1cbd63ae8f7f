# Changelog

## 0.0.322 (2024-10-15)

## What's Changed
* feat: Add new documentation files by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3351
* fix: separate english and french ingredients by @chloedia in https://github.com/QuivrHQ/quivr/pull/3358
* docs(core): init by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3365
* docs: quivr core storage by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3366
* fix: fixing pdf parsing by @j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in https://github.com/QuivrHQ/quivr/pull/3349
* feat: Improve user credit calculation in get_user_credits by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3367
* fix unwanted parsing effect by @chloedia in https://github.com/QuivrHQ/quivr/pull/3371
* add fallback on llamaparse by @chloedia in https://github.com/QuivrHQ/quivr/pull/3374


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.321...v0.0.322

## 0.0.321 (2024-10-08)

## What's Changed
* feat: Add file name to knowledge properties in crawl_endpoint by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3346
* feat(frontend): add xlsx by @Zewed in https://github.com/QuivrHQ/quivr/pull/3340


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.320...v0.0.321

## 0.0.320 (2024-10-07)

## What's Changed
* Enable Porter Application cdp by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/3306
* Enable Porter Application cdp-front by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/3307
* feat(assistant): cdp by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3305
* feat: Add debug logging for OCR results by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3309
* feat: Update OCR image scale for better accuracy by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3310
* feat: Update PDFGenerator to include logo in header by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3311
* feat: structlog parseable by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3312
* Revert "feat: structlog parseable" by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3313
* feat: structlog parseable by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3314
* Revert "feat: structlog parseable" by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3315
* feat: Update PDFGenerator to include logo in header by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3318
* feat: structlog parseable by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3319
* feat: Update knowledge status and send notification on task success by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3320
* feat: Update text retranscription instructions by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3321
* feat: update sentry by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3326
* chore: Remove unnecessary assertion in create_modification_report function by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3329
* chore: Add NEXT_PUBLIC_INTERCOM_APP_ID to Dockerfile by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3330
* feat: Update Content Security Policy for PostHog by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3331
* fix(cdp): order naming cdc_etiquette by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3333
* fix(notifications): added a debouncing mechanism by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3327


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.319...v0.0.320

## 0.0.319 (2024-10-01)

## What's Changed
* feat: Add error handling for rate-limited search in fetch_notion_pages by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3298
* feat: Remove syncNotion from ConnectionCards by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3300


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.318...v0.0.319

## 0.0.318 (2024-09-30)

## What's Changed
* feat: add syncNotion from ConnectionCards by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3292


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.317...v0.0.318

## 0.0.317 (2024-09-30)

## What's Changed
* feat: update SyncsUser status field to be optional by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3284
* fix(frontend): onboarding modal by @Zewed in https://github.com/QuivrHQ/quivr/pull/3286
* feat: Remove syncNotion from ConnectionCards by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3289


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.316...v0.0.317

## 0.0.316 (2024-09-30)

## What's Changed
* fix(core): enforce langchain <0.3 for pydantic v1 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3217
* chore(main): release core 0.0.16 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3218
* feat(assistants): mock api by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3195
* chore(examples): fix chainlit example  by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3223
* feat(integration): Notion by @chloedia in https://github.com/QuivrHQ/quivr/pull/3173
* fix(migration): order migrations by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3231
* feat: remove n+1 query knowledge by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3238
* feat: introducing configurable retrieval workflows by @jacopo-chevallard in https://github.com/QuivrHQ/quivr/pull/3227
* fix: wrong default path for CHAT_LLM_CONFIG_PATH env variable by @jacopo-chevallard in https://github.com/QuivrHQ/quivr/pull/3247
* fix(frontend): sync folder for premium users by @Zewed in https://github.com/QuivrHQ/quivr/pull/3251
* fix: correctly passing the prompt provided by the user by @jacopo-chevallard in https://github.com/QuivrHQ/quivr/pull/3252
* fix: add sync user id aget_files by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3261
* feat: update sync_user.py to include sync user id in aget_files call by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3276
* chore: update Dockerfile dependencies and copy files by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3277
* chore: add wget to Dockerfile dependencies by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3279


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.315...v0.0.316

## 0.0.315 (2024-09-17)

## What's Changed
* chore(main): release core 0.0.15 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3203
* fix: knowledge user_id fix by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3216


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.314...v0.0.315

## 0.0.314 (2024-09-16)

## What's Changed
* feat:  CRUD KMS (no syncs)  by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3162


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.313...v0.0.314

## 0.0.313 (2024-09-13)

## What's Changed
* feat: save and load brain by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3202


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.312...v0.0.313

## 0.0.312 (2024-09-13)

## What's Changed
* fix: Update LLMEndpoint to include max_tokens parameter by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3201


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.311...v0.0.312

## 0.0.311 (2024-09-12)

## What's Changed
* chore(embeddings): added tests for embeddings by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3183
* feat(uptime): check if connection to db works by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3199


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.310...v0.0.311

## 0.0.310 (2024-09-10)

## What's Changed
* feat: Add Azure OpenAI embeddings support by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3182


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.309...v0.0.310

## 0.0.309 (2024-09-10)

## What's Changed
* chore: Add initial documentation files and configuration by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3126
* chore: Add .readthedocs.yaml configuration file by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3172
* fix(frontend): notion integration in front end by @Zewed in https://github.com/QuivrHQ/quivr/pull/3175
* chore(main): release core 0.0.14 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2945
* ci(rye): now core is built with rye by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3177
* feat: Add external Supabase URL support for generating file signed URL by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3179
* fix(onboarding): keeps setting it at false by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3180


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.308...v0.0.309

## 0.0.308 (2024-09-06)

## What's Changed
* fix: remove knowledge and idle conn by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3165


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.307...v0.0.308

## 0.0.307 (2024-09-06)

## What's Changed
* fix: tests pytest-asyncio by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3157
* fix: remove unused 'models' field in check_premium.py by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3164


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.306...v0.0.307

## 0.0.306 (2024-09-05)

## What's Changed
* feat(readme): trigger deploy by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3159


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.305...v0.0.306

## 0.0.305 (2024-09-05)

## What's Changed
* chore: update next.config.js with PostHog domains by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3155


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.304...v0.0.305

## 0.0.304 (2024-09-05)

## What's Changed
* feat: update Azure login button text by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3152
* fix: url knowledge multiple brain by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3145
* chore: update Dockerfile with PostHog environment variables by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3154


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.303...v0.0.304

## 0.0.303 (2024-09-04)

## What's Changed
* fix(chat): order of chat history was reversed by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3148


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.302...v0.0.303

## 0.0.302 (2024-09-04)

## What's Changed
* feat(anthropic): add llm by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3146


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.301...v0.0.302

## 0.0.301 (2024-09-03)

## What's Changed
* feat(smtp): not enforcing tls by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3067
* feat: Update send_email.py to conditionally login with SMTP credentials by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3069
* feat: Add NEXT_PUBLIC_PROJECT_NAME environment variable by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3070
* feat: Add Azure login support by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3071
* fix(frontend): only owner can access knowledge and edit brain snippet by @Zewed in https://github.com/QuivrHQ/quivr/pull/3073
* fix(frontend): brain knowledge visibility by @Zewed in https://github.com/QuivrHQ/quivr/pull/3074
* fix(frontend): revamp of settings  by @Zewed in https://github.com/QuivrHQ/quivr/pull/3081
* fix: Update README.md by @Zewed in https://github.com/QuivrHQ/quivr/pull/3084
* fix(frontend): send invitation clearer by @Zewed in https://github.com/QuivrHQ/quivr/pull/3090
* fix(frontend): spacing chats by @Zewed in https://github.com/QuivrHQ/quivr/pull/3091
* fix(frontend): use click delay by @Zewed in https://github.com/QuivrHQ/quivr/pull/3092
* fix(frontend): back buttons by @Zewed in https://github.com/QuivrHQ/quivr/pull/3094
* fix(frontend): change hover delay for tooltips by @Zewed in https://github.com/QuivrHQ/quivr/pull/3095
* fix(frontend): rename search by thread by @Zewed in https://github.com/QuivrHQ/quivr/pull/3099
* feat(frontend): revamp of some basics components by @Zewed in https://github.com/QuivrHQ/quivr/pull/3105
* feat(frontend): interaction with brain items by @Zewed in https://github.com/QuivrHQ/quivr/pull/3106
* fix(frontend): remove intercom on thread page of mobile by @Zewed in https://github.com/QuivrHQ/quivr/pull/3108
* feat:  quivr core 0.1 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2970
* fix: Blank scrollbar on certain browsers by @adityanandanx in https://github.com/QuivrHQ/quivr/pull/3118
* chore(docs): moved repository by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3123
* fix: knowledge user by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3124
* chore(readme): increase size icon by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3125
* chore: remove unused syncNotion function in ConnectionCards.tsx by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3129
* chore: update compatibility by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3136
* ci(raise): only on tags by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3140
* feat: using langgraph in our RAG pipeline by @jacopo-chevallard in https://github.com/QuivrHQ/quivr/pull/3130
* fix: knowledge multiple brains by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3142
* fix: knowledge multiple brains - update knowledge rollback by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3143

## New Contributors
* @jacopo-chevallard made their first contribution in https://github.com/QuivrHQ/quivr/pull/3130

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.300...v0.0.301

## 0.0.300 (2024-08-22)

## What's Changed
* feat(smtp&share): implemented smtp support and fixed share brain by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3049


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.299...v0.0.300

## 0.0.299 (2024-08-22)

## What's Changed
* fix: Update Supabase configuration by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3014
* Delete Porter Application theodo-backend by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/3022
* Delete Porter Application theodo-frontend by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/3023
* feat(frontend): emoji selector component by @Zewed in https://github.com/QuivrHQ/quivr/pull/3024
* feat(frontend): show or hide tokens relative stuff by @Zewed in https://github.com/QuivrHQ/quivr/pull/3017
* feat(frontend): color picker by @Zewed in https://github.com/QuivrHQ/quivr/pull/3027
* fix(frontend): small stuff on mobile by @Zewed in https://github.com/QuivrHQ/quivr/pull/3039
* feat(frontend): handle LaTeX in message thread by @Zewed in https://github.com/QuivrHQ/quivr/pull/3040
* fix(frontend): fix docker build frontend new env by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3041
* feat(frontend): brain snippet selector by @Zewed in https://github.com/QuivrHQ/quivr/pull/3038
* feat: Add environment variable for showing tokens in frontend by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3048
* fix(frontend): color icon for blocks by @Zewed in https://github.com/QuivrHQ/quivr/pull/3042


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.298...v0.0.299

## 0.0.298 (2024-08-16)

## What's Changed
* feat(azure): quivr compatible with it by @StanGirard in https://github.com/QuivrHQ/quivr/pull/3005
* feat(frontend): helpbox by @Zewed in https://github.com/QuivrHQ/quivr/pull/3007
* feat(frontend): order models and brain by name by @Zewed in https://github.com/QuivrHQ/quivr/pull/3009
* fix(frontend): help window by @Zewed in https://github.com/QuivrHQ/quivr/pull/3013


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.297...v0.0.298

## 0.0.297 (2024-08-14)

## What's Changed
* fix(frontend): remove onboarding modal by @Zewed in https://github.com/QuivrHQ/quivr/pull/3001
* fix(frontend): max height for user message by @Zewed in https://github.com/QuivrHQ/quivr/pull/3000
* fix: commit on try sqlalchemy by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/3004


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.296...v0.0.297

## 0.0.296 (2024-08-13)

## What's Changed
* fix(frontend): color of copy icon for code blocks by @Zewed in https://github.com/QuivrHQ/quivr/pull/2993
* fix(frontend): search bar height by @Zewed in https://github.com/QuivrHQ/quivr/pull/2998
* fix: autocommit isolation level by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2999


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.295...v0.0.296

## 0.0.295 (2024-08-12)

## What's Changed
* chore: Update frontend README.md with yarn dev command (#2931) by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2958
* feat: Add brain_id and brain_name to ChatLLMMetadata model by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2968
* feat(frontend): talk with models and handle code markdown by @Zewed in https://github.com/QuivrHQ/quivr/pull/2980
* fix(frontend): talk with models by @Zewed in https://github.com/QuivrHQ/quivr/pull/2981
* feat(models): all models by default by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2983
* chore(env): add deactivate stripe env variable by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2986
* fix(frontend): handling unimported languages in Prism js by @Zewed in https://github.com/QuivrHQ/quivr/pull/2990
* fix(frontend): logo design of models by @Zewed in https://github.com/QuivrHQ/quivr/pull/2992


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.294...v0.0.295

## 0.0.294 (2024-08-07)

## What's Changed
* Delete Porter Application quivr-com by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2927
* Delete Porter Application quivr-com-backend by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2928
* feat: quivr core tox test + parsers by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2929
* feat(frontend): handle no brain selection by @Zewed in https://github.com/QuivrHQ/quivr/pull/2932
* fix: processor quivr version by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2934
* fix: quivr core fix tests by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2935
* chore(main): release core 0.0.13 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2930
* feat: Add GitHub sync functionality to sync router by @chloedia in https://github.com/QuivrHQ/quivr/pull/2871
* refactor: Remove syncGitHub function from useSync.ts by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2942
* feat: add chat with models by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2933
* ci: precommit in CI by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2946
* feat: Add get_model method to ModelRepository by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2949
* feat: Add user email to StripePricingOrManageButton and UpgradeToPlusButton components by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2951


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.293...v0.0.294

## 0.0.293 (2024-07-30)

## What's Changed
* feat(frontend): brain carousel by @Zewed in https://github.com/QuivrHQ/quivr/pull/2924
* fix(frontend): feedback for brain carousel by @Zewed in https://github.com/QuivrHQ/quivr/pull/2926


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.292...v0.0.293

## 0.0.292 (2024-07-29)

## What's Changed
* Feat/handle_azure_sites by @chloedia in https://github.com/QuivrHQ/quivr/pull/2921
* feat: update dev command in frontend README.md by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2922


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.291...v0.0.292

## 0.0.291 (2024-07-29)

## What's Changed
* feat(integrations): Add Azure Drive Sites support by @chloedia in https://github.com/QuivrHQ/quivr/pull/2919
* chore: update sync notification status to INFO by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2918


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.290...v0.0.291

## 0.0.290 (2024-07-26)

## What's Changed
* fix(frontend): remove possibility to sync folder by @Zewed in https://github.com/QuivrHQ/quivr/pull/2913
* fix(frontend): remove latence for delete notifications by @Zewed in https://github.com/QuivrHQ/quivr/pull/2916
* fix(backend): fix error messages by @Zewed in https://github.com/QuivrHQ/quivr/pull/2917
* feat(notifications): improved requirements by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2915


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.289...v0.0.290

## 0.0.289 (2024-07-25)

## What's Changed
* chore(main): release core 0.0.12 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2889
* fix: error dict google drive by @chloedia in https://github.com/QuivrHQ/quivr/pull/2912


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.288...v0.0.289

## 0.0.288 (2024-07-23)

## What's Changed
* fix(sync): folder upload to Google Drive by @chloedia in https://github.com/QuivrHQ/quivr/pull/2909


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.287...v0.0.288

## 0.0.287 (2024-07-23)

## What's Changed
* feat: Update AzureDriveSync name to "Share Point" by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2907


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.286...v0.0.287

## 0.0.286 (2024-07-23)

## What's Changed
* fix(frontend): remove warning in frontend by @Zewed in https://github.com/QuivrHQ/quivr/pull/2896
* feat(frontend): add logo to source when integration by @Zewed in https://github.com/QuivrHQ/quivr/pull/2899
* feat(premium): add trialing by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2900
* feat: Remove unused onboarding code by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2901
* feat(dead-code): removed composite & api by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2902
* feat(frontend): add a loader when processing files by @Zewed in https://github.com/QuivrHQ/quivr/pull/2903
* feat(frontend): icon chevron for folders by @Zewed in https://github.com/QuivrHQ/quivr/pull/2904
* feat(frontend): loader icon for integrations by @Zewed in https://github.com/QuivrHQ/quivr/pull/2905
* fix(frontend): remove current brain id when click on create brain by @Zewed in https://github.com/QuivrHQ/quivr/pull/2906


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.285...v0.0.286

## 0.0.285 (2024-07-22)

## What's Changed
* fix(frontend): select line when click on knowledge option by @Zewed in https://github.com/QuivrHQ/quivr/pull/2879
* fix(frontend): selected item on mention list by @Zewed in https://github.com/QuivrHQ/quivr/pull/2881
* fix(frontend): logo color on dark mode by @Zewed in https://github.com/QuivrHQ/quivr/pull/2882
* fix: Refacto & update dropbox refresh by @chloedia in https://github.com/QuivrHQ/quivr/pull/2875
* feat: quivr core brain info + processors registry +  by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2877
* chore(main): release core 0.0.10 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2872
* feat: move parsers quivr core by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2884
* fix(frontend): default icon for knowledge without extension by @Zewed in https://github.com/QuivrHQ/quivr/pull/2887
* chore(main): release core 0.0.11 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2886
* feat(frontend): add knowledge icon when integration by @Zewed in https://github.com/QuivrHQ/quivr/pull/2888
* fix: google conversion by @chloedia in https://github.com/QuivrHQ/quivr/pull/2891
* fix(frontend): remove thoughts button by @Zewed in https://github.com/QuivrHQ/quivr/pull/2892
* feat: add integration and integration link to Sources class by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2893
* feat(azure): changed auth method by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2890
* feat(frontend): new inputs by @Zewed in https://github.com/QuivrHQ/quivr/pull/2894
* fix(frontend): menu on mobile by @Zewed in https://github.com/QuivrHQ/quivr/pull/2895
* feat(api): add logging for inactive subscriptions by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2897


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.284...v0.0.285

## 0.0.284 (2024-07-17)

## What's Changed
* chore(cleanup): cleaned up some unused files by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2858
* feat: Improve file loading logic in File model by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2861
* feat(celery): add retry logic for dcos by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2862
* feat: quivr api send notification by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2865
* feat: Update category for sync notification by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2866
* feat: Update crawl_endpoint to include knowledge_id in task kwargs by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2867
* feat(integrations): Add integration fields to Knowledge and SyncsUser models by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2853
* feat: quivr api use quivr core by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2868
* feat: Update sync generic bulk by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2869
* chore(main): release core 0.0.9 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2855
* feat(frontend): new notifications design by @Zewed in https://github.com/QuivrHQ/quivr/pull/2870
* feat: Update sync active notification category by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2873
* feat(integrations): dropbox by @chloedia in https://github.com/QuivrHQ/quivr/pull/2864
* fix(frontend): delete notifications by @Zewed in https://github.com/QuivrHQ/quivr/pull/2874
* feat(frontend): back button by @Zewed in https://github.com/QuivrHQ/quivr/pull/2876


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.283...v0.0.284

## 0.0.283 (2024-07-12)

## What's Changed
* feat: Add bulk_id field to CreateNotification and Notification models by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2850
* feat: Add bulk_id field to CreateNotification and Notification models by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2854
* fix: quiv core stream duplicate  and quivr-core rag tests by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2852
* feat: Add category and brain_id fields to CreateNotification and Notification models by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2856
* feat: Update file loading logic in File model by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2857


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.282...v0.0.283

## 0.0.282 (2024-07-11)

## What's Changed
* chore: Update quivr-core and chainlit versions in requirements.txt by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2839
* chore(config): migrate renovate config by @renovate in https://github.com/QuivrHQ/quivr/pull/2838
* fix: fixes duplicate response bug by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2843
* fix(frontend): remove possibilities to create brain if max amount by @Zewed in https://github.com/QuivrHQ/quivr/pull/2847
* fix(frontend): general before connections by @Zewed in https://github.com/QuivrHQ/quivr/pull/2848
* fix(backend): thumbs button were broken by @Zewed in https://github.com/QuivrHQ/quivr/pull/2849


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.281...v0.0.282

## 0.0.281 (2024-07-11)

## What's Changed
* feat: quivr core minimal chat by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2803
* chore: Add release-please-core workflow and configuration files by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2809
* chore(main): release core 0.0.2 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2812
* chore(main): release core 0.0.2 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2813
* chore(main): release core 0.0.2 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2814
* chore(main): release core 0.0.2 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2815
* fix(pyproject): fixed to quivr github by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2816
* chore(main): release core 0.0.3 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2817
* feat: quivr core minimal chat by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2818
* chore(main): release core 0.0.4 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2819
* feat: Add GitHub Actions workflow for running tests on backend/core by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2820
* feat: Add GitHub Actions workflow for running tests on backend/core by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2822
* feat(precommit): Update pre-commit hooks to latest versions by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2823
* feat: quivr core chat history by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2824
* chore(main): release core 0.0.5 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2821
* feat(frontend): UI / UX Notifications by @Zewed in https://github.com/QuivrHQ/quivr/pull/2826
* feat: quivr-core ask streaming by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2828
* chore(main): release core 0.0.6 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2829
* fix: llm model name by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2830
* chore(main): release core 0.0.7 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2831
* feat: Add Quivr chatbot example by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2827
* feat(renovate): updated configuration by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2835
* feat: Update aiofiles dependency to loosen version control by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2834
* chore(main): release core 0.0.8 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2832
* chore: Update quivr-core and chainlit versions in requirements.txt by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2836


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.280...v0.0.281

## 0.0.280 (2024-07-09)

## What's Changed
* feat(frontend): add small property to textInput by @Zewed in https://github.com/QuivrHQ/quivr/pull/2798
* fix(frontend): brain studio ui audit implem by @Zewed in https://github.com/QuivrHQ/quivr/pull/2800
* fix(frontend): remove quivr assistant for now by @Zewed in https://github.com/QuivrHQ/quivr/pull/2801
* feat(frontend): Uniformize brain icon by @Zewed in https://github.com/QuivrHQ/quivr/pull/2802
* fix(frontend): out of credits when credits null by @Zewed in https://github.com/QuivrHQ/quivr/pull/2804
* feat(frontend): better UI for big modals by @Zewed in https://github.com/QuivrHQ/quivr/pull/2805
* fix(frontend): align brain recap step by @Zewed in https://github.com/QuivrHQ/quivr/pull/2806
* feat: Improve error handling in acquiring token by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2807


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.279...v0.0.280

## 0.0.279 (2024-07-04)

## What's Changed
* feat(frontend): remove create brain step in onboarding when share brain by @Zewed in https://github.com/QuivrHQ/quivr/pull/2791
* feat: add megaparse by @chloedia in https://github.com/QuivrHQ/quivr/pull/2785
* fix: asyncpg pooling config fix by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2795
* fix: RAG service bug assertion error by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2796


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.278...v0.0.279

## 0.0.278 (2024-07-01)

## What's Changed
* chore: Add GitHub Actions workflow for testing and building Docker image by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2778
* chore: Update flashrank npm dependency to version 0.2.5 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2781
* feat(frontend): disabled searchBar if no remaining credits or no brain selected by @Zewed in https://github.com/QuivrHQ/quivr/pull/2788


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.277...v0.0.278

## 0.0.277 (2024-06-27)

## What's Changed
* fix(frontend): UI Notification Ring and Fix Page Header Display on Mobile by @Zewed in https://github.com/QuivrHQ/quivr/pull/2771
* feat(frontend): better UI for General Settings by @Zewed in https://github.com/QuivrHQ/quivr/pull/2773
* fix(frontend): new api key creation bug by @Zewed in https://github.com/QuivrHQ/quivr/pull/2774


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.276...v0.0.277

## 0.0.276 (2024-06-27)

## What's Changed
* chore: Add supabase directory to Dockerfile by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2768
* chore: Update docker-compose files to specify platform for backend services by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2763
* Revert "chore: Update docker-compose files to specify platform for backend services" by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2770


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.275...v0.0.276

## 0.0.275 (2024-06-27)

## What's Changed
* fix(frontend): fix table in frontend by @Zewed in https://github.com/QuivrHQ/quivr/pull/2758
* chore: Update docker-compose files to specify platform for backend services by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2762
* fix(frontend): settings button by @Zewed in https://github.com/QuivrHQ/quivr/pull/2764
* feat(backend):   quivr-monorepo and quivr-core package by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2765
* chore: Add ci-migration.sh to Dockerfile by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2767


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.274...v0.0.275

## 0.0.274 (2024-06-26)

## What's Changed
* refacto(backend): poetry package manager and chat route refactoring  by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2684
* closes #2756 by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2757
* fix: Update email sender parameters in backend code by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2755


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.273...v0.0.274

## 0.0.273 (2024-06-25)

## What's Changed
* feat(frontend): new ui ux for knowledge by @Zewed in https://github.com/QuivrHQ/quivr/pull/2732
* fix(frontend): small issues with scss by @Zewed in https://github.com/QuivrHQ/quivr/pull/2734


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.272...v0.0.273

## 0.0.272 (2024-06-24)

## What's Changed
* fix(frontend): send empty sync is not allowed by @Zewed in https://github.com/QuivrHQ/quivr/pull/2716
* feat: Improve efficiency of syncing stripe by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2719


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.271...v0.0.272

## 0.0.271 (2024-06-24)

## What's Changed
* fix: check user premium upsert by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2714


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.270...v0.0.271

## 0.0.270 (2024-06-24)

## What's Changed
* fix(frontend): small rephrase by @Zewed in https://github.com/QuivrHQ/quivr/pull/2699
* feat(frontend): table markdown by @Zewed in https://github.com/QuivrHQ/quivr/pull/2702
* Enable Porter Application raise by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2705
* Enable Porter Application raise-frontend by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2706
* chore: Remove Porter stack deployment workflows for cherry-pick-backend and cherry-pick-frontend by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2707
* fix(frontend): 25 instead of 100 by @Zewed in https://github.com/QuivrHQ/quivr/pull/2708
* fix(frontend): remove double scss class by @elazarnaaman in https://github.com/QuivrHQ/quivr/pull/2704
* fix(frontend): documents before connections by @Zewed in https://github.com/QuivrHQ/quivr/pull/2711
* feat(frontend): brain studio interface by @Zewed in https://github.com/QuivrHQ/quivr/pull/2712


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.269...v0.0.270

## 0.0.269 (2024-06-20)

## What's Changed
* feat: Add Microsoft Identity Association JSON file by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2697


**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.268...v0.0.269

## 0.0.268 (2024-06-18)

## What's Changed

- fix(frontend): rephrase unpaid to free by @Zewed in https://github.com/QuivrHQ/quivr/pull/2679
- feat(frontend): set from connections by default in knowledge to feed by @Zewed in https://github.com/QuivrHQ/quivr/pull/2680
- feat(frontend): rephrase from Url to from Website's page by @Zewed in https://github.com/QuivrHQ/quivr/pull/2684
- feat(frontend): new chat interface by @Zewed in https://github.com/QuivrHQ/quivr/pull/2687
- fix(frontend): next step impossible if no knowledge when creating brain if no onboarded by @Zewed in https://github.com/QuivrHQ/quivr/pull/2688
- feat(turbopack): Implement turbo pack compiler by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2685
- fix(frontend): rephrase thoughts button title by @Zewed in https://github.com/QuivrHQ/quivr/pull/2689

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.267...v0.0.268

## 0.0.267 (2024-06-14)

## What's Changed

- fix(frontend): scroll modal payment by @Zewed in https://github.com/QuivrHQ/quivr/pull/2675

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.266...v0.0.267

## 0.0.266 (2024-06-13)

## What's Changed

- fix: Add logic to filter active subscriptions in check_if_is_premium_user function by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2673

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.265...v0.0.266

## 0.0.265 (2024-06-13)

## What's Changed

- feat: Add timezone conversion for premium user check by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2670

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.264...v0.0.265

## 0.0.264 (2024-06-13)

## What's Changed

- fix: name passed in sync authorize by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2665
- feat: Add premium user check in celery task by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2668

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.263...v0.0.264

## 0.0.263 (2024-06-12)

## What's Changed

- fix: Add error handling for syncing in tasks.py by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2663

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.262...v0.0.263

## 0.0.262 (2024-06-12)

## What's Changed

- Update README.md by @ferozemohideen in https://github.com/QuivrHQ/quivr/pull/2660
- feat: Normalize file names in sync module by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2661

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.261...v0.0.262

## 0.0.261 (2024-06-11)

## What's Changed

- feat: Update Google authorization URL with prompt for consent by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2658

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.260...v0.0.261

## 0.0.260 (2024-06-11)

## What's Changed

- feat: Add extra_hosts configuration to docker-compose.dev.yml by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2635
- fix: sync creation fixed by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2637
- chore: Set default value for "last_synced" column in "syncs_active" table to '2024-06-01 15:30:25+00' by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2638
- fix: integrations by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2642
- feat(frontend): sharepoint and gdrive integration by @Zewed in https://github.com/QuivrHQ/quivr/pull/2643
- fix(frontend): display bug on add knowledge by @Zewed in https://github.com/QuivrHQ/quivr/pull/2644
- fix: files_metadata by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2645
- fix(google): auth is now in state by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2647
- fix(frontend): add brain modal integration doestn t work by @Zewed in https://github.com/QuivrHQ/quivr/pull/2649
- fix(frontend): tooltip on folder line by @Zewed in https://github.com/QuivrHQ/quivr/pull/2650
- feat: telemetry improved by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2651
- feat: Add force_sync option to SyncsActiveUpdateInput by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2652
- Update license to include enterprise features by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2653
- fix(frontend): onboarding bug by @Zewed in https://github.com/QuivrHQ/quivr/pull/2655
- Update README.md by @ferozemohideen in https://github.com/QuivrHQ/quivr/pull/2656

## New Contributors

- @ferozemohideen made their first contribution in https://github.com/QuivrHQ/quivr/pull/2656

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.259...v0.0.260

## 0.0.259 (2024-06-04)

## What's Changed

- feat(upload): async improved by @AmineDiro in https://github.com/QuivrHQ/quivr/pull/2544

## New Contributors

- @AmineDiro made their first contribution in https://github.com/QuivrHQ/quivr/pull/2544

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.258...v0.0.259

## 0.0.258 (2024-05-29)

## What's Changed

- feat: Update QuivrRAG and run_evaluation.py files by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2615
- fix: modify thought prompt by @chloedia in https://github.com/QuivrHQ/quivr/pull/2626
- feat(llamaparse): Update parsing instructions in common.py by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2627
- feat(sync): retrieve user email used for the connection by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2628
- fix: Refactor conversational_qa_chain initialization in KnowledgeBrainQA by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2629

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.257...v0.0.258

## 0.0.257 (2024-05-28)

## What's Changed

- Add Privacy & Compliance Documentation by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2620
- docs(security): added compliance by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2621
- fix(frontend): upgrade button on user page by @Zewed in https://github.com/QuivrHQ/quivr/pull/2623
- feat(frontend): Add ThoughtsButton component for displaying thoughts by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2624

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.256...v0.0.257

## 0.0.256 (2024-05-26)

## What's Changed

- feat(rag): follow-up questions and thoughts with spanish fix by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2618

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.255...v0.0.256

## 0.0.255 (2024-05-24)

## What's Changed

- feat: Add Google Drive & Sharepoint sync in backend by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2592
- Revert "feat: Add Google Drive & Sharepoint sync in backend" by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2603
- Feat/auth-playground by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2605
- feat: add init to create packages by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2606
- Add additional modules to celery.autodiscover_tasks() by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2607
- Feat/celery import by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2608
- feat: self-reflect brain by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2610
- feat: ragas improved testing by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2611
- fix(frontend): less agressive colors by @Zewed in https://github.com/QuivrHQ/quivr/pull/2612
- fix(frontend): important buttons by @Zewed in https://github.com/QuivrHQ/quivr/pull/2613
- fix(frontend): fix white colors by @Zewed in https://github.com/QuivrHQ/quivr/pull/2614
- fix(frontend): using dark mode in tiptap by @ramonzaca in https://github.com/QuivrHQ/quivr/pull/2616

## New Contributors

- @ramonzaca made their first contribution in https://github.com/QuivrHQ/quivr/pull/2616

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.254...v0.0.255

## 0.0.254 (2024-05-21)

## What's Changed

- fix: sender email address in resend_invitation_email.py by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2600

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.253...v0.0.254

## 0.0.253 (2024-05-14)

## What's Changed

- fix(frontend): Implement persistent dark mode setting and & Implement persistent dark mode setting by @elazarnaaman in https://github.com/QuivrHQ/quivr/pull/2423
- fix(frontend): hover effect on profile Button by @Zewed in https://github.com/QuivrHQ/quivr/pull/2587
- fix(frontend): user invite UI on Mobile by @Zewed in https://github.com/QuivrHQ/quivr/pull/2586
- feat: Update ChatLiteLLM model and add RLS optimization for notifications by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2591

## New Contributors

- @elazarnaaman made their first contribution in https://github.com/QuivrHQ/quivr/pull/2423

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.252...v0.0.253

## 0.0.252 (2024-05-13)

## What's Changed

- docs: Update GPT4 documentation with available tools and use cases by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2580
- docs: Add docstrings to integration brains by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2582
- fix: Update import statements for OllamaEmbeddings by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2584
- feat: Add support for gpt-4o model by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2589

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.251...v0.0.252

## 0.0.251 (2024-05-10)

## What's Changed

- feat(tool): Add URLReaderTool by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2577
- feat(email): Add email sender tool and update image generator tool by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2579

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.250...v0.0.251

## 0.0.250 (2024-05-10)

## What's Changed

- feat(gpt4): Add search functionality by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2566
- [ImgBot] Optimize images by @imgbot in https://github.com/QuivrHQ/quivr/pull/2568
- feat(gpt4): image generation by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2569
- fix(front): Add NEXT_PUBLIC_AUTH_MODES to .env.example by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2570
- fix: NEXT_PUBLIC_AUTH_MODES in docker-compose.yml by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2572
- docs: Add Supabase configuration documentation by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2574
- docs(gpt4): Update GPT-4 Documentation by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2573
- chore: tools by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2575
- feat(brave-search): Update GPT4Brain tools and add WebSearchTool by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2576

## New Contributors

- @imgbot made their first contribution in https://github.com/QuivrHQ/quivr/pull/2568

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.249...v0.0.250

## 0.0.249 (2024-05-08)

## What's Changed

- feat(crawler): Add Playwright for web crawling by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2562
- ci(ecr): added build to public ecr by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2564

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.248...v0.0.249

## 0.0.248 (2024-05-07)

## What's Changed

- fix: utf8 encoding by @chloedia in https://github.com/QuivrHQ/quivr/pull/2555
- feat(celery): moved assistant summary to celery by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2557
- Revert "feat(celery): moved assistant summary to celery" by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2558

## New Contributors

- @chloedia made their first contribution in https://github.com/QuivrHQ/quivr/pull/2555

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.247...v0.0.248

## 0.0.247 (2024-05-07)

## What's Changed

- feat(frontend): add notifications for document uploads by @Zewed in https://github.com/QuivrHQ/quivr/pull/2549
- Update install.mdx by @dazeb in https://github.com/QuivrHQ/quivr/pull/2552
- fix(frontend): fix notifications issues by @Zewed in https://github.com/QuivrHQ/quivr/pull/2551
- chore(ci): Update PR title linting workflow by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2553

## New Contributors

- @dazeb made their first contribution in https://github.com/QuivrHQ/quivr/pull/2552

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.246...v0.0.247

## 0.0.246 (2024-05-04)

## What's Changed

- Enable Porter Application theodo-backend by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2537
- Enable Porter Application theodo-frontend by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2538
- Add config parameter to conversational_qa_chain by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2540
- feat(notion): update doc by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2542

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.245...v0.0.246

## 0.0.245 (2024-05-03)

## What's Changed

- feat(user): Delete User Data from frontend by @Zewed in https://github.com/QuivrHQ/quivr/pull/2476
- feat(backend): Add a pre_pring on Connection polling to handle disconnection by @dmourot in https://github.com/QuivrHQ/quivr/pull/2534
- feat(llama-parse): improve prompt by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2535
- feat(brain): Add ProxyBrain integration by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2536

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.244...v0.0.245

## 0.0.244 (2024-05-02)

## What's Changed

- fix: Update parsing instruction in common.py by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2531

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.243...v0.0.244

## 0.0.243 (2024-05-01)

## What's Changed

- fix: Refactor chat_service.py and remove unused code by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2530

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.242...v0.0.243

## 0.0.242 (2024-05-01)

## What's Changed

- feat(notifications): implemented notifications with RLS and realtime by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2525
- chore: packages by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2527
- Enable Porter Application production by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2528

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.241...v0.0.242

## 0.0.241 (2024-05-01)

## What's Changed

- feat(llamaparse): Add Llama Parse integration for complex document parsing by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2517
- Delete Porter Application quivr-back by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2519
- Delete Porter Application quivr-demo-front by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2520
- Enable Porter Application preview by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2521
- Enable Porter Application preview-frontend by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2522
- feat(frontend): citations & sources by @Zewed in https://github.com/QuivrHQ/quivr/pull/2523
- Fix: citation handling in ChatItem component by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2524

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.240...v0.0.241

## 0.0.240 (2024-04-29)

## What's Changed

- feat(supabase): Add logging statements and refactor Supabase client creation by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2514
- feat(backend): use SQLAlchemy instead od supabase API by @dmourot in https://github.com/QuivrHQ/quivr/pull/2516

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.239...v0.0.240

## 0.0.239 (2024-04-28)

## What's Changed

- feat(citations): system added by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2498
- feat(frontend): add nb of knowledges per brain by @Zewed in https://github.com/QuivrHQ/quivr/pull/2502
- docs: Update links in mint.json to add api by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2504
- feat(docker): Update Dockerfile to install Supabase CLI by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2505
- fix(frontend): fix some next errors by @Zewed in https://github.com/QuivrHQ/quivr/pull/2503
- feat(frontend): show remaining credits by @Zewed in https://github.com/QuivrHQ/quivr/pull/2495
- feat(embedding): keeping citations by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2506
- fix(metadata): Removed citation from metadata by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2507
- Add ci-migration script by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2508
- Feat/migration ci 2 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2509
- Enable Porter Application quivr-com-backend by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2510
- Enable Porter Application quivr-com by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2511
- feat(profiler): Add pyinstrument package and update Makefile and backend code by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2512
- feat(db): Add Supabase client and database instances caching by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2513

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.238...v0.0.239

## 0.0.238 (2024-04-25)

## What's Changed

- Enable Porter Application cherry-pick-backend by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2492
- Enable Porter Application cherry-pick-frontend by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2493
- feat: Add telemetry ping task to celery worker and main.py by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2494
- fix(backend): compute history only if needed and put some cache to remove some call… by @dmourot in https://github.com/QuivrHQ/quivr/pull/2497

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.237...v0.0.238

## 0.0.237 (2024-04-24)

## What's Changed

- docs: Add environment variables, increase user usage, and add new models by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2481
- fix(frontend): Warning for Quivr Assistants by @Zewed in https://github.com/QuivrHQ/quivr/pull/2479
- fix(frontend): better UI for Onboarding by @Zewed in https://github.com/QuivrHQ/quivr/pull/2477
- docs: add new configuration items by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2483
- Revert "fix(frontend): better UI for Onboarding" by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2485
- feat(reranker): Add flashrank and contextual compression retriever by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2480
- feat(history): max tokens in the history provided by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2487
- feat: Update chunk overlap to 200 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2488
- docs: Add reranking configuration guide by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2489
- docs: Update telemetry configuration in Quivr by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2490

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.236...v0.0.237

## 0.0.236 (2024-04-23)

## What's Changed

- feat(docs): update to new by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2465
- feat(docs): Add new brain files and update navigation by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2467
- Feat/docs category brains agents by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2469
- fix(docs): update doc by @Zewed in https://github.com/QuivrHQ/quivr/pull/2470
- feat(digital-ocean): Update deployment instructions for Digital Ocean by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2472
- docs(digital-ocean): added missing photo by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2473
- docs: Update brain documentation and images by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2475

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.235...v0.0.236

## 0.0.235 (2024-04-21)

## What's Changed

- fix(sources): Remove duplicate sources and add metadata to model response by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2462

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.234...v0.0.235

## 0.0.234 (2024-04-21)

## What's Changed

- fix(gpt4): Refactor GPT4Brain and KnowledgeBrainQA classes to add non-streaming-saving-answer by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2460

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.233...v0.0.234

## 0.0.233 (2024-04-21)

## What's Changed

- refactor: reorg files in backend by @MaximeThoonsen in https://github.com/QuivrHQ/quivr/pull/2449
- Revert "refactor: reorg files in backend" by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2456
- refactor: Refacto code #1 by @MaximeThoonsen in https://github.com/QuivrHQ/quivr/pull/2458
- refactor: reorg the files #2 by @MaximeThoonsen in https://github.com/QuivrHQ/quivr/pull/2457
- feat(gpt4): Add chat service and generate answer method to GPT4Brain class by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2459

## New Contributors

- @MaximeThoonsen made their first contribution in https://github.com/QuivrHQ/quivr/pull/2449

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.232...v0.0.233

## 0.0.232 (2024-04-19)

## What's Changed

- Update CPU and memory settings in task definition files by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2450
- fix: Fix error message in SummaryAssistant class by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2453

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.231...v0.0.232

## 0.0.231 (2024-04-19)

## What's Changed

- feat(assistants): Add user usage update and pricing calculation to ITO assistant by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2433
- feat(assistant): improve prompt summary by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2435
- feat(assistants): Add PDF generation functionality and nice emails by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2436
- feat(analytics): rely on sql rather that python loop for brains by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2437
- fix(assistant): summary now can output 2000 tokens by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2440
- feat(assistant): check if key of file is same as filename uploaded by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2439
- feat: Update Docker build commands and dependencies by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2441
- feat(rag): Refactor DEFAULT_DOCUMENT_PROMPT in quivr_rag.py by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2442
- Enable Porter Application quivr-back by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2443
- Enable Porter Application quivr-demo-front by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2444
- fix(assistants): brain id is null by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2445
- feat(summary): improve prompt to get more insights by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2446
- feat(aws): Update CPU and memory configurations for task definitions by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2447
- feat(frontend): Quivr Assistants by @Zewed in https://github.com/QuivrHQ/quivr/pull/2448

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.230...v0.0.231

## 0.0.230 (2024-04-16)

## What's Changed

- feat(backend): add RAG evaluation using Ragas by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2429
- feat(assistants): Add new input models for boolean, number, select text, and select number by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2432

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.229...v0.0.230

## 0.0.229 (2024-04-12)

## What's Changed

- feat: optimization calls by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2417
- feat: Add assistant module and remove ingestion module by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2420
- feat: assistants by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2421
- feat: Add tags to AssistantOutput classes by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2425
- feat: Add icon and description to assistant by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2426
- feat: llamaparse & diff agent by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2427

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.228...v0.0.229

## 0.0.228 (2024-04-10)

## What's Changed

- fix(frontend): phone display issues by @Zewed in https://github.com/QuivrHQ/quivr/pull/2386
- Patch 1 by @llwp in https://github.com/QuivrHQ/quivr/pull/2388
- fix: typo in README.md by @bolens in https://github.com/QuivrHQ/quivr/pull/2391
- feat(ingestion): Add ingestion module and routes by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2393
- feat: Add seed ingestions to supabase migrations by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2399
- feat: Add url_required field to IngestionEntity by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2400
- Feat: Bibtex file uploads by @colesnic in https://github.com/QuivrHQ/quivr/pull/2398
- fix: logger level and telemetry function calls by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2409
- fix: Add integration brain to subscription route by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2410
- feat(frontend): onboarding V2 by @Zewed in https://github.com/QuivrHQ/quivr/pull/2394
- fix(frontend): onboardind bug by @Zewed in https://github.com/QuivrHQ/quivr/pull/2414
- fix(frontend): cleaner fix for onboarding by @Zewed in https://github.com/QuivrHQ/quivr/pull/2415
- feat(analytics): added analytics page by @Zewed in https://github.com/QuivrHQ/quivr/pull/2416

## New Contributors

- @llwp made their first contribution in https://github.com/QuivrHQ/quivr/pull/2388
- @bolens made their first contribution in https://github.com/QuivrHQ/quivr/pull/2391
- @colesnic made their first contribution in https://github.com/QuivrHQ/quivr/pull/2398

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.227...v0.0.228

## 0.0.227 (2024-03-28)

## What's Changed

- fix(backend): invitation with new brains did not work by @Zewed in https://github.com/QuivrHQ/quivr/pull/2378
- fix(backend): invitation brain bugs by @Zewed in https://github.com/QuivrHQ/quivr/pull/2380
- fix(frontend): disable knowledge tab by @Zewed in https://github.com/QuivrHQ/quivr/pull/2381
- fix(frontend): dark mode issues by @Zewed in https://github.com/QuivrHQ/quivr/pull/2382
- feat(frontend): show icons only on hover except for last message by @Zewed in https://github.com/QuivrHQ/quivr/pull/2377

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.226...v0.0.227

## 0.0.226 (2024-03-21)

## What's Changed

- feat: Add Mistral models to defineMaxTokens and BrainConfig by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2363
- feat: mistral by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2365
- fix(retriever): Update match_vectors sql function to rank chunks in correct order by @dmourot in https://github.com/QuivrHQ/quivr/pull/2367
- feat(docker image): Docker image Optimized for CPU-only env by @dmourot in https://github.com/QuivrHQ/quivr/pull/2368
- feat(frontend): dark mode by @Zewed in https://github.com/QuivrHQ/quivr/pull/2369
- feat(frontend & backend): thumbs for message feedback by @Zewed in https://github.com/QuivrHQ/quivr/pull/2360
- fix(backend): migration legacy by @Zewed in https://github.com/QuivrHQ/quivr/pull/2370
- fix(frontend): type stripe casing by @Zewed in https://github.com/QuivrHQ/quivr/pull/2371
- fix(backend): unsubscribe from brain by @Zewed in https://github.com/QuivrHQ/quivr/pull/2373
- feat(frontend): onboarding form by @Zewed in https://github.com/QuivrHQ/quivr/pull/2342
- fix(frontend): onBoarding issue by @Zewed in https://github.com/QuivrHQ/quivr/pull/2374

## New Contributors

- @dmourot made their first contribution in https://github.com/QuivrHQ/quivr/pull/2367

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.225...v0.0.226

## 0.0.225 (2024-03-15)

## What's Changed

- fix(frontend): bigger icon on message row by @Zewed in https://github.com/QuivrHQ/quivr/pull/2345
- fix(frontend): remove brains usage in user page by @Zewed in https://github.com/QuivrHQ/quivr/pull/2349

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.224...v0.0.225

## 0.0.224 (2024-03-15)

## What's Changed

- feat(frontend): add discord link by @Zewed in https://github.com/QuivrHQ/quivr/pull/2343
- fix(frontend): upgrade to plus by @Zewed in https://github.com/QuivrHQ/quivr/pull/2346

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.223...v0.0.224

## 0.0.223 (2024-03-13)

## What's Changed

- chore: update packages backend by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2339
- feat: Add optional fields to UserIdentity and UserUpdatableProperties by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2341

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.222...v0.0.223

## 0.0.222 (2024-03-09)

## What's Changed

- feat: Update langchain.prompts and langchain_core.messages modules by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2326
- feat(frontend): social buttons by @Zewed in https://github.com/QuivrHQ/quivr/pull/2325
- fix(frontend): upgrade to plus button by @Zewed in https://github.com/QuivrHQ/quivr/pull/2324
- fix(frontend): maximum amount of brains reached by @Zewed in https://github.com/QuivrHQ/quivr/pull/2323

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.221...v0.0.222

## 0.0.221 (2024-03-07)

## What's Changed

- feat: seed updated by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2313
- fix(frontend): allow model change by @Zewed in https://github.com/QuivrHQ/quivr/pull/2317
- fix(frontend): mention list by @Zewed in https://github.com/QuivrHQ/quivr/pull/2315
- fix(frontend): studio to brain studio by @Zewed in https://github.com/QuivrHQ/quivr/pull/2316
- feat(frontend): add help tooltip for model selection by @Zewed in https://github.com/QuivrHQ/quivr/pull/2318
- fix(frontend): page header studio to brain studio by @Zewed in https://github.com/QuivrHQ/quivr/pull/2319

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.220...v0.0.221

## 0.0.220 (2024-03-06)

## What's Changed

- fix(frontend): brain name by @Zewed in https://github.com/QuivrHQ/quivr/pull/2311

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.219...v0.0.220

## 0.0.219 (2024-03-06)

## What's Changed

- feat: Update to newest version of litellm by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2307
- fix(frontend): small renaiming chat to thread by @Zewed in https://github.com/QuivrHQ/quivr/pull/2306
- feat(frontend): brain Catalogue by @Zewed in https://github.com/QuivrHQ/quivr/pull/2303
- feat(frontend): 404 redirection by @Zewed in https://github.com/QuivrHQ/quivr/pull/2309
- fix(frontend): old brain legacy by @Zewed in https://github.com/QuivrHQ/quivr/pull/2310

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.218...v0.0.219

## 0.0.218 (2024-03-05)

## What's Changed

- feat: doc as integration by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2297
- fix(rag): add api_base by @niels-garve in https://github.com/QuivrHQ/quivr/pull/2289
- fix(frontend): login bug by @Zewed in https://github.com/QuivrHQ/quivr/pull/2300

## New Contributors

- @niels-garve made their first contribution in https://github.com/QuivrHQ/quivr/pull/2289

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.217...v0.0.218

## 0.0.217 (2024-03-04)

## What's Changed

- fix(frontend): fix home page redirection by @Zewed in https://github.com/QuivrHQ/quivr/pull/2295

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.216...v0.0.217

## 0.0.216 (2024-03-04)

## What's Changed

- feat: Update chunk_size in File model by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2281
- fix(frontend): double file upload on drag and drop by @Zewed in https://github.com/QuivrHQ/quivr/pull/2284
- fix(frontend): click anywhere on drop zone to upload file by @Zewed in https://github.com/QuivrHQ/quivr/pull/2285
- fix(frontend): smalls on thread section by @Zewed in https://github.com/QuivrHQ/quivr/pull/2286
- fix(frontend): remove tests by @Zewed in https://github.com/QuivrHQ/quivr/pull/2287
- feat(frontend): better UI/UX on select brain by @Zewed in https://github.com/QuivrHQ/quivr/pull/2288
- feat(frontend): add brain icon on brain list by @Zewed in https://github.com/QuivrHQ/quivr/pull/2292
- fix(frontend): whitespace on firefox by @Zewed in https://github.com/QuivrHQ/quivr/pull/2293
- fix(frontend): remove unused stuff by @Zewed in https://github.com/QuivrHQ/quivr/pull/2282

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.215...v0.0.216

## 0.0.215 (2024-03-01)

## What's Changed

- fix(frontend): message info box by @Zewed in https://github.com/QuivrHQ/quivr/pull/2277
- fix(frontend): see knowledge in custom brains by @Zewed in https://github.com/QuivrHQ/quivr/pull/2278
- fix(frontend): fix disabled knwoledge tab by @Zewed in https://github.com/QuivrHQ/quivr/pull/2280

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.214...v0.0.215

## 0.0.214 (2024-02-29)

## What's Changed

- fix(frontend): revamp quivr studio by @Zewed in https://github.com/QuivrHQ/quivr/pull/2274
- fix(frontend): zindex and radius on single selector component by @Zewed in https://github.com/QuivrHQ/quivr/pull/2276

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.213...v0.0.214

## 0.0.213 (2024-02-28)

## What's Changed

- feat(notion): added custom integration by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2268
- feat: Remove constraints and add foreign key references to brain tables by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2273

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.212...v0.0.213

## 0.0.212 (2024-02-26)

## What's Changed

- feat: new landing page by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2264

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.211...v0.0.212

## 0.0.211 (2024-02-24)

## What's Changed

- fix: ollama migration documentation by @bidoubiwa in https://github.com/QuivrHQ/quivr/pull/2248
- Update Sentry configuration and ignore file by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2250
- Fix Sentry DSN environment variable by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2251
- fix(sentry): Refactor GlobalError component to use arrow function syntax by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2252
- fix: Update Sentry configuration to use NEXT_PUBLIC_SENTRY_DSN by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2254
- feat(frontend): integrate octolane by @Zewed in https://github.com/QuivrHQ/quivr/pull/2256
- fix(frontend): better search bar and chat box by @Zewed in https://github.com/QuivrHQ/quivr/pull/2255
- feat(frontend): sources per messages by @Zewed in https://github.com/QuivrHQ/quivr/pull/2253
- feat(sentry): remove health endpoint by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2257
- Add octolane.com to Content Security Policy by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2258
- fix(frontend): ui chat box & sources small bug by @Zewed in https://github.com/QuivrHQ/quivr/pull/2260
- Refactor GitHub workflows by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2261

## New Contributors

- @bidoubiwa made their first contribution in https://github.com/QuivrHQ/quivr/pull/2248

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.210...v0.0.211

## 0.0.210 (2024-02-22)

## What's Changed

- feat: Update memory allocation in task definition by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2243
- fix: get_brain_details API to include user_id parameter by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2242
- feat(chat): Add follow up questions functionality by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2241
- Reduce sampling rate for Sentry traces by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2245
- Revert "feat(chat): Add follow up questions functionality" by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2246
- Add max_input and max_tokens parameters to KnowledgeBrainQA constructor by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2247

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.209...v0.0.210

## 0.0.209 (2024-02-22)

## What's Changed

- fix(frontend): fix share brain by @Zewed in https://github.com/QuivrHQ/quivr/pull/2238
- fix(frontend): don't preselect core brain by @Zewed in https://github.com/QuivrHQ/quivr/pull/2239

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.208...v0.0.209

## 0.0.208 (2024-02-21)

## What's Changed

- feat: Add pricing calculation method to GPT4Brain class and update user usage in chat controller by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2216
- Enable Porter Application quivr by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2220
- Delete Porter Application quivr by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2221
- Enable Porter Application preview-quivr by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2222
- Enable Porter Application prod-quivr by @porter-deployment-app in https://github.com/QuivrHQ/quivr/pull/2223
- feat(brains): added description by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2224
- feat: Add integration_logo_url to MinimalUserBrainEntity by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2225
- Add Redis configuration to celery_config.py by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2227
- Remove unused 'model' variable and logging statements by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2228
- feat: Add max_files attribute to MinimalUserBrainEntity and BrainsUsers repository by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2229
- Feature: Add animations to foldable section by @johnfewell in https://github.com/QuivrHQ/quivr/pull/2202
- feat(frontend): first custom brain live by @Zewed in https://github.com/QuivrHQ/quivr/pull/2226
- fix(frontend): legacy on foldable section animation pr by @Zewed in https://github.com/QuivrHQ/quivr/pull/2230
- Fix: API endpoint for getting integration brains by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2231
- feat: Update dependencies and remove unnecessary logging statements by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2232
- feat: implement elasticache by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2234
- fix(frontend): ellipsis overflow on large brain or prompt names by @Zewed in https://github.com/QuivrHQ/quivr/pull/2233

## New Contributors

- @porter-deployment-app made their first contribution in https://github.com/QuivrHQ/quivr/pull/2220
- @johnfewell made their first contribution in https://github.com/QuivrHQ/quivr/pull/2202

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.207...v0.0.208

## 0.0.206 (2024-02-19)

## What's Changed

- feat: Add pricing calculation method to GPT4Brain class and update user usage in chat controller by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2210
- fix(frontend): click on inputs by @Zewed in https://github.com/QuivrHQ/quivr/pull/2212

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.205...v0.0.206

## 0.0.205 (2024-02-19)

## What's Changed

- Update ollama.mdx by @zangjiucheng in https://github.com/QuivrHQ/quivr/pull/2196
- feat(integration): improve by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2199
- fix(frontend): history to threads by @Zewed in https://github.com/QuivrHQ/quivr/pull/2201
- feat(custom): big brain by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2198
- feat: Update system templates with custom personality support by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2209

## New Contributors

- @zangjiucheng made their first contribution in https://github.com/QuivrHQ/quivr/pull/2196

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.204...v0.0.205

## 0.0.203 (2024-02-15)

## What's Changed

- feat: 🎸 ocr by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2187
- feat(lcel): migrated to lcel and pydantic by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2185
- feat(frontend): new brain creation modal by @Zewed in https://github.com/QuivrHQ/quivr/pull/2192
- feat(integration): implementation by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2191
- feat(frontend): new design for brain table by @Zewed in https://github.com/QuivrHQ/quivr/pull/2193

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.202...v0.0.203

## 0.0.202 (2024-02-11)

## What's Changed

- fix(frontend): change placeholder in chat bar by @Zewed in https://github.com/QuivrHQ/quivr/pull/2177
- fix(frontend): remove notification banner by @Zewed in https://github.com/QuivrHQ/quivr/pull/2178
- fix(frontend): remove onboarding questions by @Zewed in https://github.com/QuivrHQ/quivr/pull/2176
- feat(frontend): new modal for add knowledge by @Zewed in https://github.com/QuivrHQ/quivr/pull/2173
- Revert "fix(frontend): remove onboarding questions" by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2181
- fix(frontend): remove onboarding question by @Zewed in https://github.com/QuivrHQ/quivr/pull/2183

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.201...v0.0.202

## 0.0.201 (2024-02-10)

## What's Changed

- fix: 🐛 session by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2174

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.200...v0.0.201

## 0.0.200 (2024-02-09)

## What's Changed

- fix(daily-usage): Update daily requests count in UserUsage model by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2171

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.199...v0.0.200

## 0.0.199 (2024-02-08)

## What's Changed

- feat: 🎸 telemetry by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2169

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.198...v0.0.199

## 0.0.197 (2024-02-07)

## What's Changed

- fix(prompts): can now be removed by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2154
- tests: Add tests for deleting prompts by ID by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2156
- fix(related): removed public brains by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2157
- perf: ⚡️ signed_url by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2159
- fix(backend): typo in word response by @untilhamza in https://github.com/QuivrHQ/quivr/pull/2158
- fix(frontend): better UI for phone device by @Zewed in https://github.com/QuivrHQ/quivr/pull/2160
- fix(frontend): add knwoledge from create brain by @Zewed in https://github.com/QuivrHQ/quivr/pull/2161
- feat(chunks): now chunk size is saved in database dynamically and not just 500 by @StanGirard in https://github.com/QuivrHQ/quivr/pull/2164
- fix(frontend): remove related brains for now by @Zewed in https://github.com/QuivrHQ/quivr/pull/2162
- fix(frontend): can"t choose private or public brains by @Zewed in https://github.com/QuivrHQ/quivr/pull/2163
- feat(frontend): manage current brain by @Zewed in https://github.com/QuivrHQ/quivr/pull/2165
- fix(frontend): upgrade my plan by @Zewed in https://github.com/QuivrHQ/quivr/pull/2167

## New Contributors

- @untilhamza made their first contribution in https://github.com/QuivrHQ/quivr/pull/2158

**Full Changelog**: https://github.com/QuivrHQ/quivr/compare/v0.0.196...v0.0.197

## 0.0.196 (2024-02-07)

## What's Changed

- feat(frontend): Page Header + Begin of Studio by @Zewed in https://github.com/StanGirard/quivr/pull/2151
- fix(frontend): overflow brain item by @Zewed in https://github.com/StanGirard/quivr/pull/2153

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.195...v0.0.196

## 0.0.195 (2024-02-06)

## What's Changed

- feat(integrations): integration with Notion in the backend by @StanGirard in https://github.com/StanGirard/quivr/pull/2123

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.194...v0.0.195

## 0.0.194 (2024-02-05)

## What's Changed

- feat(frontend): add a chatbot for users by @Zewed in https://github.com/StanGirard/quivr/pull/2144

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.193...v0.0.194

## 0.0.193 (2024-02-04)

## What's Changed

- feat(frontend): design changes on user profile by @Zewed in https://github.com/StanGirard/quivr/pull/2140
- fix(frontend): rename upgrade to plus to upgrade by @Zewed in https://github.com/StanGirard/quivr/pull/2141

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.192...v0.0.193

## 0.0.192 (2024-02-02)

## What's Changed

- feat(frontend): display which brain you are talking to by @Zewed in https://github.com/StanGirard/quivr/pull/2137

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.191...v0.0.192

## 0.0.191 (2024-02-01)

## What's Changed

- fix(frontend): no sources repetition in data panel by @Zewed in https://github.com/StanGirard/quivr/pull/2132
- fix(frontend): don't show copy icon when thinking by @Zewed in https://github.com/StanGirard/quivr/pull/2133

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.190...v0.0.191

## 0.0.190 (2024-01-31)

## What's Changed

- fix(frontend): Better contrast in Menu by @Zewed in https://github.com/StanGirard/quivr/pull/2119
- fix(frontend): better chat color and copy icon position by @Zewed in https://github.com/StanGirard/quivr/pull/2121
- fix(frontend): better visualisation of current path on menu by @Zewed in https://github.com/StanGirard/quivr/pull/2122
- feat(frontend): uniformize behaviour for metadata panel by @Zewed in https://github.com/StanGirard/quivr/pull/2124
- fix(frontend): fetch chat only if session exist by @Zewed in https://github.com/StanGirard/quivr/pull/2130
- fix(frontend): prompt display by @Zewed in https://github.com/StanGirard/quivr/pull/2129
- fix(frontend): upload knwoledge in brains manegement by @Zewed in https://github.com/StanGirard/quivr/pull/2131

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.189...v0.0.190

## 0.0.189 (2024-01-30)

## What's Changed

- feat(frontend): design menu by @Zewed in https://github.com/StanGirard/quivr/pull/2116
- fix(frontend): fix z index popover on add knwoledge modal by @Zewed in https://github.com/StanGirard/quivr/pull/2118

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.188...v0.0.189

## 0.0.188 (2024-01-29)

## What's Changed

- fix: 🐛 upload by @StanGirard in https://github.com/StanGirard/quivr/pull/2112
- feat(frontend): add sources to metadata by @Zewed in https://github.com/StanGirard/quivr/pull/2113

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.187...v0.0.188

## 0.0.187 (2024-01-28)

## What's Changed

- feat: 🎸 user-limits by @StanGirard in https://github.com/StanGirard/quivr/pull/2104
- fix: 🐛 brains by @StanGirard in https://github.com/StanGirard/quivr/pull/2107
- feat(frontend): chat page ui/ux design by @Zewed in https://github.com/StanGirard/quivr/pull/2106
- Fix typo of UI: Ressources ==> Resources by @iharel in https://github.com/StanGirard/quivr/pull/2109
- fix(frontend): fix li markdown on chat by @Zewed in https://github.com/StanGirard/quivr/pull/2110

## New Contributors

- @iharel made their first contribution in https://github.com/StanGirard/quivr/pull/2109

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.186...v0.0.187

## 0.0.186 (2024-01-27)

## What's Changed

- fix(frontend): click on mention list on search bar in modal bug by @Zewed in https://github.com/StanGirard/quivr/pull/2098
- fix(frontend): remove dark theme by @Zewed in https://github.com/StanGirard/quivr/pull/2100
- fix(frontend): delete brain by @Zewed in https://github.com/StanGirard/quivr/pull/2101
- feat(14k): done by @StanGirard in https://github.com/StanGirard/quivr/pull/2102

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.185...v0.0.186

## 0.0.185 (2024-01-27)

## What's Changed

- fix: 🐛 brain by @StanGirard in https://github.com/StanGirard/quivr/pull/2096
- feat(frontend): search modal - remove parameters and explore buttons by @Zewed in https://github.com/StanGirard/quivr/pull/2094
- fix: 🐛 tests by @StanGirard in https://github.com/StanGirard/quivr/pull/2095

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.184...v0.0.185

## 0.0.184 (2024-01-26)

## What's Changed

- feat(panel): added by @Zewed in https://github.com/StanGirard/quivr/pull/2088
- feat: 🎸 api by @StanGirard in https://github.com/StanGirard/quivr/pull/2078
- fix(frontend): clear message input on submit by @Zewed in https://github.com/StanGirard/quivr/pull/2087
- fix: 🐛 related by @StanGirard in https://github.com/StanGirard/quivr/pull/2090
- feat: Added translation status badge from inlang by @NilsJacobsen in https://github.com/StanGirard/quivr/pull/2080
- fix(streaming): Data Truncation Issue in useHandleStream Function by @openperf in https://github.com/StanGirard/quivr/pull/2079
- feat: 🎸 sources by @StanGirard in https://github.com/StanGirard/quivr/pull/2092
- fix(frontend): clean related Brains useEffect by @Zewed in https://github.com/StanGirard/quivr/pull/2091

## New Contributors

- @openperf made their first contribution in https://github.com/StanGirard/quivr/pull/2079

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.183...v0.0.184

## 0.0.183 (2024-01-24)

## What's Changed

- fix: 🐛 subscription by @StanGirard in https://github.com/StanGirard/quivr/pull/2081

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.182...v0.0.183

## 0.0.182 (2024-01-24)

## What's Changed

- fix: 🐛 crawl by @StanGirard in https://github.com/StanGirard/quivr/pull/2076

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.181...v0.0.182

## 0.0.181 (2024-01-23)

## What's Changed

- fix(frontend): unable multiple enter on search page by @Zewed in https://github.com/StanGirard/quivr/pull/2074
- fix(frontend): force brain on search was broken by @Zewed in https://github.com/StanGirard/quivr/pull/2075

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.180...v0.0.181

## 0.0.180 (2024-01-23)

## What's Changed

- fix: 🐛 api by @StanGirard in https://github.com/StanGirard/quivr/pull/2068
- feat(frontend): Add Brain On Search Page by @Zewed in https://github.com/StanGirard/quivr/pull/2067
- fix(frontend): uniformize case for types by @Zewed in https://github.com/StanGirard/quivr/pull/2071
- fix: 🐛 gitconfig by @StanGirard in https://github.com/StanGirard/quivr/pull/2072

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.179...v0.0.180

## 0.0.179 (2024-01-22)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.178...v0.0.179

## 0.0.178 (2024-01-22)

## What's Changed

- fix(frontend): use mention brain on search bar by @Zewed in https://github.com/StanGirard/quivr/pull/2060
- feat: 🎸 cpu by @StanGirard in https://github.com/StanGirard/quivr/pull/2065

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.177...v0.0.178

## 0.0.177 (2024-01-22)

## What's Changed

- fix: 🐛 usage by @StanGirard in https://github.com/StanGirard/quivr/pull/2062

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.176...v0.0.177

## 0.0.176 (2024-01-22)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.175...v0.0.176

## 0.0.175 (2024-01-22)

## What's Changed

- fix: 🐛 limits by @StanGirard in https://github.com/StanGirard/quivr/pull/2058

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.174...v0.0.175

## 0.0.174 (2024-01-22)

## What's Changed

- fix(frontend): remove actions modal by @Zewed in https://github.com/StanGirard/quivr/pull/2054
- feat: 🎸 usage by @StanGirard in https://github.com/StanGirard/quivr/pull/2057

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.173...v0.0.174

## 0.0.173 (2024-01-22)

## What's Changed

- fix(api-brains): fixed with new types of brains by @StanGirard in https://github.com/StanGirard/quivr/pull/2052
- fix(frontend): font size on chat feed by @Zewed in https://github.com/StanGirard/quivr/pull/2051

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.172...v0.0.173

## 0.0.172 (2024-01-22)

## What's Changed

- feat(frontend): handle mentions in search bar by @Zewed in https://github.com/StanGirard/quivr/pull/2049

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.171...v0.0.172

## 0.0.171 (2024-01-22)

## What's Changed

- fix(frontend): don't set default brain as current brain and remove change brain button by @Zewed in https://github.com/StanGirard/quivr/pull/2047
- feat: 🎸 brains by @StanGirard in https://github.com/StanGirard/quivr/pull/2048

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.170...v0.0.171

## 0.0.170 (2024-01-21)

## What's Changed

- fix(frontend): translate configure for brains and successfully deleted for chat by @Zewed in https://github.com/StanGirard/quivr/pull/2042
- fix(frontend): change search icon on menu by @Zewed in https://github.com/StanGirard/quivr/pull/2043
- fix: 🐛 search by @StanGirard in https://github.com/StanGirard/quivr/pull/2045
- fix(frontend): remove brain choice when search page displays by @Zewed in https://github.com/StanGirard/quivr/pull/2044

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.169...v0.0.170

## 0.0.169 (2024-01-21)

## What's Changed

- feat(brains): added now multiple brains close by by @StanGirard in https://github.com/StanGirard/quivr/pull/2039
- fix(frontend): set chat messages only if needed by @Zewed in https://github.com/StanGirard/quivr/pull/2040

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.168...v0.0.169

## 0.0.168 (2024-01-20)

## What's Changed

- fix(frontend): don t load chat items on search by @Zewed in https://github.com/StanGirard/quivr/pull/2036

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.167...v0.0.168

## 0.0.167 (2024-01-20)

## What's Changed

- fix(frontend): chat still refreshs on first request by @Zewed in https://github.com/StanGirard/quivr/pull/2034

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.166...v0.0.167

## 0.0.166 (2024-01-20)

## What's Changed

- feat(search): new way to interact with Quivr by @StanGirard in https://github.com/StanGirard/quivr/pull/2026
- feat: adding search by @StanGirard in https://github.com/StanGirard/quivr/pull/2031
- fix(frontend): remove close menu icon on mobile by @Zewed in https://github.com/StanGirard/quivr/pull/2030
- fix(frontend): chat refreshed on first search request by @Zewed in https://github.com/StanGirard/quivr/pull/2033

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.165...v0.0.166

## 0.0.165 (2024-01-17)

## What's Changed

- fix(frontend): wrong placeholder message date by @Zewed in https://github.com/StanGirard/quivr/pull/2023

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.164...v0.0.165

## 0.0.164 (2024-01-14)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.163...v0.0.164

## 0.0.163 (2024-01-14)

## What's Changed

- fix(frontend): left panel is not closing by @Zewed in https://github.com/StanGirard/quivr/pull/2014
- fix: csp by @StanGirard in https://github.com/StanGirard/quivr/pull/2016

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.162...v0.0.163

## 0.0.162 (2024-01-13)

## What's Changed

- fix(frontend): remove right panel and reduce chat section width by @Zewed in https://github.com/StanGirard/quivr/pull/2012

## New Contributors

- @Zewed made their first contribution in https://github.com/StanGirard/quivr/pull/2012

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.161...v0.0.162

## 0.0.161 (2024-01-07)

## What's Changed

- feat: 🎸 policies by @StanGirard in https://github.com/StanGirard/quivr/pull/1997

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.160...v0.0.161

## 0.0.160 (2024-01-04)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.159...v0.0.160

## 0.0.159 (2024-01-04)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.158...v0.0.159

## 0.0.158 (2024-01-04)

## What's Changed

- chore(deps): pin dependencies by @renovate in https://github.com/StanGirard/quivr/pull/1975

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.157...v0.0.158

## 0.0.157 (2024-01-04)

## What's Changed

- feat: 🎸 posthog by @StanGirard in https://github.com/StanGirard/quivr/pull/1978

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.156...v0.0.157

## 0.0.156 (2024-01-04)

## What's Changed

- fix: 🐛 models by @StanGirard in https://github.com/StanGirard/quivr/pull/1973

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.155...v0.0.156

## 0.0.155 (2024-01-04)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.154...v0.0.155

## 0.0.154 (2024-01-04)

## What's Changed

- feat: 🎸 models by @StanGirard in https://github.com/StanGirard/quivr/pull/1967

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.153...v0.0.154

## 0.0.153 (2024-01-03)

## What's Changed

- chore(deps): Pin Node.js by @renovate in https://github.com/StanGirard/quivr/pull/1952
- chore(deps): Pin dependencies by @renovate in https://github.com/StanGirard/quivr/pull/1953
- chore(deps): Update actions/checkout action to v4 by @renovate in https://github.com/StanGirard/quivr/pull/1957
- chore(deps): Update actions/setup-node action to v4 by @renovate in https://github.com/StanGirard/quivr/pull/1958
- feat: 🎸 usage by @StanGirard in https://github.com/StanGirard/quivr/pull/1966

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.152...v0.0.153

## 0.0.152 (2024-01-02)

## What's Changed

- feat: 🎸 posthog by @StanGirard in https://github.com/StanGirard/quivr/pull/1945

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.151...v0.0.152

## 0.0.151 (2023-12-29)

## What's Changed

- feat: 🎸 posthog by @StanGirard in https://github.com/StanGirard/quivr/pull/1938

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.150...v0.0.151

## 0.0.150 (2023-12-29)

## What's Changed

- feat: 🎸 posthog by @StanGirard in https://github.com/StanGirard/quivr/pull/1936

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.149...v0.0.150

## 0.0.149 (2023-12-29)

## What's Changed

- feat: 🎸 pricing by @StanGirard in https://github.com/StanGirard/quivr/pull/1935

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.148...v0.0.149

## 0.0.148 (2023-12-28)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.147...v0.0.148

## 0.0.147 (2023-12-28)

## What's Changed

- feat: 🎸 posthog by @StanGirard in https://github.com/StanGirard/quivr/pull/1931

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.146...v0.0.147

## 0.0.146 (2023-12-28)

## What's Changed

- feat: 🎸 posthog by @StanGirard in https://github.com/StanGirard/quivr/pull/1929

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.145...v0.0.146

## 0.0.145 (2023-12-28)

## What's Changed

- feat: 🎸 posthog by @StanGirard in https://github.com/StanGirard/quivr/pull/1927

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.144...v0.0.145

## 0.0.144 (2023-12-27)

## What's Changed

- feat: 🎸 pricing by @StanGirard in https://github.com/StanGirard/quivr/pull/1923
- feat(pricing): added testimonials and else by @StanGirard in https://github.com/StanGirard/quivr/pull/1925

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.143...v0.0.144

## 0.0.143 (2023-12-27)

## What's Changed

- docs: ✏️ mintlify by @StanGirard in https://github.com/StanGirard/quivr/pull/1917
- feat(docs): added homepage by @StanGirard in https://github.com/StanGirard/quivr/pull/1919
- docs: ✏️ homepage by @StanGirard in https://github.com/StanGirard/quivr/pull/1922

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.142...v0.0.143

## 0.0.142 (2023-12-18)

## What's Changed

- feat: add new brain management page by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1906
- feat: update brain details page by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1910

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.141...v0.0.142

## 0.0.141 (2023-12-15)

## What's Changed

- feat[i18n]: Added i18n documenation to the contribution guidelines by @NilsJacobsen in https://github.com/StanGirard/quivr/pull/1899
- feat: Update Explore button label by @StanGirard in https://github.com/StanGirard/quivr/pull/1901
- feat: chat with compositeBrain ( with/out streaming) by @gozineb in https://github.com/StanGirard/quivr/pull/1883
- feat: update brains library by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1903

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.140...v0.0.141

## 0.0.140 (2023-12-14)

## What's Changed

- feat: Update pytest command in Makefile and add new test by @StanGirard in https://github.com/StanGirard/quivr/pull/1893
- chore: add IDE extension for i18n handling by @NilsJacobsen in https://github.com/StanGirard/quivr/pull/1896
- feat: add chat view new design by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1897

## New Contributors

- @NilsJacobsen made their first contribution in https://github.com/StanGirard/quivr/pull/1896

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.139...v0.0.140

## 0.0.139 (2023-12-14)

## What's Changed

- feat: allow user to feed brain from Actions bar by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1882
- feat: add Menu bar by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1885
- feat: Remove unused method and update file processing by @StanGirard in https://github.com/StanGirard/quivr/pull/1890
- fix: update chat history fetching logic by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1891
- feat: add default feed button label by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1892

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.138...v0.0.139

## 0.0.138 (2023-12-13)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.137...v0.0.138

## 0.0.137 (2023-12-13)

## What's Changed

- feat: add chat history to Actions modal by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1877
- feat: allow user to control left panel from Chat input by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1880

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.136...v0.0.137

## 0.0.136 (2023-12-13)

## What's Changed

- feat: Add @tailwindcss/forms plugin and update by @StanGirard in https://github.com/StanGirard/quivr/pull/1869
- feat: Refactor get_question_context_for_brain endpoint by @StanGirard in https://github.com/StanGirard/quivr/pull/1872
- feat: Add file URL to DocumentAnswer objects by @StanGirard in https://github.com/StanGirard/quivr/pull/1874
- Update .gitignore and add .gitmodules by @StanGirard in https://github.com/StanGirard/quivr/pull/1875
- feat: add new actions modal by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1870
- feat: add selected brain tag and new discussion button to actions modal by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1873
- feat: add action modal change brain button by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1876

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.135...v0.0.136

## 0.0.135 (2023-12-11)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.134...v0.0.135

## 0.0.134 (2023-12-11)

## What's Changed

- feat: add custom rag first abstraction layer by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1858
- feat(payment): added modal of right size by @StanGirard in https://github.com/StanGirard/quivr/pull/1860

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.133...v0.0.134

## 0.0.133 (2023-12-11)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.132...v0.0.133

## 0.0.132 (2023-12-10)

## What's Changed

- feat: add generate_answer function to support non streamed response for api brain by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1847
- fix(ollama): - update supabase-db postgres docker image version by @KonstantinosAng in https://github.com/StanGirard/quivr/pull/1853

## New Contributors

- @KonstantinosAng made their first contribution in https://github.com/StanGirard/quivr/pull/1853

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.131...v0.0.132

## 0.0.131 (2023-12-06)

## What's Changed

- feat: update onboarding questions answer by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1834
- feat: composite brains get by @gozineb in https://github.com/StanGirard/quivr/pull/1837
- feat: add Agent creation frontend by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1836
- feat: keep sidebar opened on non mobile devices by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1840
- feat: add brains list overflow indicator by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1842

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.130...v0.0.131

## 0.0.130 (2023-12-06)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.129...v0.0.130

## 0.0.129 (2023-12-06)

## What's Changed

- feat(requirements): update aws version by @StanGirard in https://github.com/StanGirard/quivr/pull/1819
- feat: add brain creation step 2 by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1823
- feat: composite brains db by @gozineb in https://github.com/StanGirard/quivr/pull/1826
- feat: finalise steps based brain creation by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1825

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.128...v0.0.129

## 0.0.128 (2023-12-05)

## What's Changed

- feat: track response source usage by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1810
- doc: add VirtioFS instruction for MacOS users by @tarek-ayed in https://github.com/StanGirard/quivr/pull/1813
- refactor: chat for multibrains by @gozineb in https://github.com/StanGirard/quivr/pull/1812
- feat(prebuilt): prebuild backend image for faster compilation by @StanGirard in https://github.com/StanGirard/quivr/pull/1815
- fix: text not clear in dark mode by @Jezla in https://github.com/StanGirard/quivr/pull/1804
- fix: celery config typo by @yonmey in https://github.com/StanGirard/quivr/pull/1776
- feat: add brain creation steps system by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1814

## New Contributors

- @tarek-ayed made their first contribution in https://github.com/StanGirard/quivr/pull/1813
- @Jezla made their first contribution in https://github.com/StanGirard/quivr/pull/1804
- @yonmey made their first contribution in https://github.com/StanGirard/quivr/pull/1776

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.127...v0.0.128

## 0.0.127 (2023-12-04)

## What's Changed

- feat(api-keys): added customization by @StanGirard in https://github.com/StanGirard/quivr/pull/1802
- feat(embedding): now 100 times faster ⚡️🔥 by @StanGirard in https://github.com/StanGirard/quivr/pull/1807
- fix: update editor state update logic by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1809

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.126...v0.0.127

## 0.0.126 (2023-12-03)

## What's Changed

- feat: add optimistic update on new message by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1764
- feat: update models logic by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1767
- refactor: to modules by @gozineb in https://github.com/StanGirard/quivr/pull/1754
- feat(supabase): local installation made easy by @StanGirard in https://github.com/StanGirard/quivr/pull/1777
- feat(install): it now takes 30 seconds to install Quivr by @StanGirard in https://github.com/StanGirard/quivr/pull/1780
- feat: 🎸 install by @StanGirard in https://github.com/StanGirard/quivr/pull/1784
- fix(url): crawling fixed by @StanGirard in https://github.com/StanGirard/quivr/pull/1785
- docs: fix typo in name of docker compose dev file by @iMADi-ARCH in https://github.com/StanGirard/quivr/pull/1800

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.125...v0.0.126

## 0.0.125 (2023-11-30)

## What's Changed

- fix(api): fixed issue with name function and ilmproved promtp by @StanGirard in https://github.com/StanGirard/quivr/pull/1759
- fix: update mention suggestion filtering logic by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1763

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.124...v0.0.125

## 0.0.124 (2023-11-29)

## What's Changed

- feat(chatInput): use tiptap editor by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1752
- docs: update guidelines.md by @eltociear in https://github.com/StanGirard/quivr/pull/1755
- Feat/local llm bug fix by @StanGirard in https://github.com/StanGirard/quivr/pull/1758

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.123...v0.0.124

## 0.0.123 (2023-11-29)

## What's Changed

- chore: downgrade versions by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1748
- fix: revert implement local llms by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1749

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.122...v0.0.123

## 0.0.122 (2023-11-29)

## What's Changed

- refactor: Notification module by @gozineb in https://github.com/StanGirard/quivr/pull/1740
- refactor: remove explore route from back & front by @gozineb in https://github.com/StanGirard/quivr/pull/1741
- feat: implement local llms by @StanGirard in https://github.com/StanGirard/quivr/pull/1745
- refactor: knowledge module by @gozineb in https://github.com/StanGirard/quivr/pull/1743

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.121...v0.0.122

## 0.0.121 (2023-11-28)

## What's Changed

- fix: update max token logic by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1725
- fix: update public brain subscription logic by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1727
- fix: sanitize file name by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1728
- feat(publicBrains): use join queries for better performance by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1730
- feat: improve delete knowledge performance by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1733
- fix: 🐛 crawler by @StanGirard in https://github.com/StanGirard/quivr/pull/1735
- feat: 🎸 local user by @StanGirard in https://github.com/StanGirard/quivr/pull/1736

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.120...v0.0.121

## 0.0.120 (2023-11-27)

## What's Changed

- fix: 🐛 sentry by @StanGirard in https://github.com/StanGirard/quivr/pull/1716

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.119...v0.0.120

## 0.0.119 (2023-11-24)

## What's Changed

- refactor: Prompt module by @gozineb in https://github.com/StanGirard/quivr/pull/1688
- Fixes string formatting when logging knowledge table by @MeTaNoV in https://github.com/StanGirard/quivr/pull/1691
- fix: update max token overwrite logic by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1694
- fix: remove diacritics from filenames by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1695
- refactor: onboarding module by @gozineb in https://github.com/StanGirard/quivr/pull/1702
- feat: display notification when file size is too big by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1704
- feat: add api brain steps log (backend) by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1705

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.118...v0.0.119

## 0.0.118 (2023-11-22)

## What's Changed

- docs: add api based brains by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1685
- Adds pytesseract, tesseract and poopler-utils by @MeTaNoV in https://github.com/StanGirard/quivr/pull/1648

## New Contributors

- @MeTaNoV made their first contribution in https://github.com/StanGirard/quivr/pull/1648

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.117...v0.0.118

## 0.0.117 (2023-11-22)

## What's Changed

- fix: 🐛 api by @StanGirard in https://github.com/StanGirard/quivr/pull/1676
- fix: persist api brain creation data on tab change by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1680
- feat: 🎸 tokens by @StanGirard in https://github.com/StanGirard/quivr/pull/1678
- feat: allow updating api brain definition by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1682
- feat: make brain description required by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1684

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.116...v0.0.117

## 0.0.116 (2023-11-21)

## What's Changed

- feat: update brain modal in chat input by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1668
- feat(apiBrain): add api brain secrets field in knowledge tab by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1669
- feat(apiBrain): improve ux by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1672
- feat(feedBrain): add manage button by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1674

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.115...v0.0.116

## 0.0.115 (2023-11-20)

## What's Changed

- fix(apiBrain): fix default type selection by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1642
- fix: allow user to set a brain as public after creation by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1646
- fix(brainManagement): fix shared brain access issue by @gozineb in https://github.com/StanGirard/quivr/pull/1641
- feat: 🎸 docker reduced size by 2 by @StanGirard in https://github.com/StanGirard/quivr/pull/1653
- feat: 🎸 docker by @StanGirard in https://github.com/StanGirard/quivr/pull/1656
- feat: 🎸 marketplace by @StanGirard in https://github.com/StanGirard/quivr/pull/1657
- feat: 🎸 openai by @StanGirard in https://github.com/StanGirard/quivr/pull/1658

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.114...v0.0.115

## 0.0.114 (2023-11-16)

## What's Changed

- feat: add api brain creation frontend by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1631
- refactor: add modules folder by @gozineb in https://github.com/StanGirard/quivr/pull/1633
- feat: update settings tab add api brain definition by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1635
- feat: add public api brain subscription by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1636
- fix: delete brain on users click in brains management by @gozineb in https://github.com/StanGirard/quivr/pull/1638

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.113...v0.0.114

## 0.0.113 (2023-11-14)

## What's Changed

- refactor: packages folder be 2 by @gozineb in https://github.com/StanGirard/quivr/pull/1628

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.112...v0.0.113

## 0.0.112 (2023-11-14)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.111...v0.0.112

## 0.0.111 (2023-11-14)

## What's Changed

- ci: 🎡 tests by @StanGirard in https://github.com/StanGirard/quivr/pull/1615
- fix: update delete brain logic by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1619
- test(added): misc prompt onboarding by @StanGirard in https://github.com/StanGirard/quivr/pull/1622
- feat: remove api brain secrets and schemas on delete by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1621
- test(all): added by @StanGirard in https://github.com/StanGirard/quivr/pull/1624
- refactor: create "files" package by @gozineb in https://github.com/StanGirard/quivr/pull/1626
- feat: api definition in brain creation modal by @gozineb in https://github.com/StanGirard/quivr/pull/1613

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.110...v0.0.111

## 0.0.110 (2023-11-13)

## What's Changed

- fix: add user id while creating default brain by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1616

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.109...v0.0.110

## 0.0.109 (2023-11-13)

## What's Changed

- feat: add APIBrainQA by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1606
- feat: allow users to chat with apis by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1612
- feat(docker): use multi-stage Docker builds for smaller images by @shidenkai0 in https://github.com/StanGirard/quivr/pull/1614

## New Contributors

- @shidenkai0 made their first contribution in https://github.com/StanGirard/quivr/pull/1614

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.108...v0.0.109

## 0.0.108 (2023-11-07)

## What's Changed

- feat: add api_brain_definition table by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1601
- feat: add brain_type column to brain table by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1603
- feat: supabase vault by @gozineb in https://github.com/StanGirard/quivr/pull/1605

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.107...v0.0.108

## 0.0.107 (2023-11-06)

## What's Changed

- fix: allow to change model bro brain settings tab by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1590
- fix: fix notification banner display when too much items in chat list by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1593
- docs: add auth modes config by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1595
- fix: allow users to delete brains by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1596
- feat: 🎸 source documents by @StanGirard in https://github.com/StanGirard/quivr/pull/1598

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.106...v0.0.107

## 0.0.106 (2023-11-06)

## What's Changed

- feat: 🎸 sources by @StanGirard in https://github.com/StanGirard/quivr/pull/1591

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.105...v0.0.106

## 0.0.105 (2023-11-06)

## What's Changed

- feat: make auth mode configurable by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1579
- Fix #1290 issue running migrations by @charlesbrandt in https://github.com/StanGirard/quivr/pull/1585
- Use 'unless-stopped' instead of 'always' for development by @charlesbrandt in https://github.com/StanGirard/quivr/pull/1586
- feat: 🎸 vps by @StanGirard in https://github.com/StanGirard/quivr/pull/1587
- Update vps_install.md for subdomain by @jbeltran73-2 in https://github.com/StanGirard/quivr/pull/1589

## New Contributors

- @charlesbrandt made their first contribution in https://github.com/StanGirard/quivr/pull/1585
- @jbeltran73-2 made their first contribution in https://github.com/StanGirard/quivr/pull/1589

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.104...v0.0.105

## 0.0.104 (2023-11-03)

## What's Changed

- feat: 🎸 docs by @StanGirard in https://github.com/StanGirard/quivr/pull/1561
- style(prompts): update public prompts dropdown styling by @St-Bloom in https://github.com/StanGirard/quivr/pull/1563
- feat: add remote notification config by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1547
- fix: fix button bad children error by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1564
- style: fix hidden contents by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1577

## New Contributors

- @St-Bloom made their first contribution in https://github.com/StanGirard/quivr/pull/1563

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.103...v0.0.104

## 0.0.103 (2023-11-02)

## What's Changed

- feat: allow to share a public brain link by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1541
- fix: prompt update in brains management settings tab by @gozineb in https://github.com/StanGirard/quivr/pull/1543
- refactor: extract prompt from settings by @gozineb in https://github.com/StanGirard/quivr/pull/1546
- feat: 🎸 telegram by @StanGirard in https://github.com/StanGirard/quivr/pull/1555
- feat: 🎸 telegram by @StanGirard in https://github.com/StanGirard/quivr/pull/1559
- docs: update run_fully_local.md by @eltociear in https://github.com/StanGirard/quivr/pull/1556
- docs: grammatical errors in README.md by @HimanshuMahto in https://github.com/StanGirard/quivr/pull/1536
- fix: missing or inaccurate zh-cn translations by @jerryshang in https://github.com/StanGirard/quivr/pull/1558

## New Contributors

- @HimanshuMahto made their first contribution in https://github.com/StanGirard/quivr/pull/1536
- @jerryshang made their first contribution in https://github.com/StanGirard/quivr/pull/1558

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.102...v0.0.103

## 0.0.102 (2023-11-01)

## What's Changed

- docs: update Quivr doc by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1531
- docs: ✏️ search by @StanGirard in https://github.com/StanGirard/quivr/pull/1535
- feat(brainSettings): rework knowledge tab by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1534
- docs: ✏️ schema by @StanGirard in https://github.com/StanGirard/quivr/pull/1537
- feat: 🎸 max-token by @StanGirard in https://github.com/StanGirard/quivr/pull/1538

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.101...v0.0.102

## 0.0.101 (2023-10-31)

## What's Changed

- chore: update tanstack query dep by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1528
- fix: update mutation pending flag by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1530

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.100...v0.0.101

## 0.0.100 (2023-10-30)

## What's Changed

- refactor: fix bad smells by @gozineb in https://github.com/StanGirard/quivr/pull/1399
- refactor: chat_routes by @gozineb in https://github.com/StanGirard/quivr/pull/1512
- feat: improve ux by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1522
- feat(docs): reworked the website by @StanGirard in https://github.com/StanGirard/quivr/pull/1523
- Feat/docs rework by @StanGirard in https://github.com/StanGirard/quivr/pull/1525

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.99...v0.0.100

## 0.0.99 (2023-10-27)

## What's Changed

- fix: minor fixes by @gozineb in https://github.com/StanGirard/quivr/pull/1499
- feat: remove onboarding's feature flag by @matthieujacq in https://github.com/StanGirard/quivr/pull/1501
- feat: update form content on magic link auth request by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1502
- feat: remove legacy header and footer by @matthieujacq in https://github.com/StanGirard/quivr/pull/1509
- fix: black horizontal line sometimes appearing below section by @matthieujacq in https://github.com/StanGirard/quivr/pull/1510
- Improve readme.md by @ankur0904 in https://github.com/StanGirard/quivr/pull/1511
- refactor(settings tab): extract components by @gozineb in https://github.com/StanGirard/quivr/pull/1335
- fix: hidden video playing automatically on iphone by @matthieujacq in https://github.com/StanGirard/quivr/pull/1514

## New Contributors

- @ankur0904 made their first contribution in https://github.com/StanGirard/quivr/pull/1511

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.98...v0.0.99

## 0.0.98 (2023-10-26)

## What's Changed

- feat: upgrade button in user settings by @matthieujacq in https://github.com/StanGirard/quivr/pull/1484
- fix: failing build (removed avatar alt prop) by @matthieujacq in https://github.com/StanGirard/quivr/pull/1487
- Fixed license link in intro.md by @Eric013 in https://github.com/StanGirard/quivr/pull/1486
- feat: manage plan by @matthieujacq in https://github.com/StanGirard/quivr/pull/1488
- feat: remove feature flags for homepage by @matthieujacq in https://github.com/StanGirard/quivr/pull/1493
- feat: add new signin/login page by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1492

## New Contributors

- @Eric013 made their first contribution in https://github.com/StanGirard/quivr/pull/1486

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.97...v0.0.98

## 0.0.97 (2023-10-24)

## What's Changed

- feat(cms): update content type by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1477
- feat: upgrade to plus button by @matthieujacq in https://github.com/StanGirard/quivr/pull/1482
- feat: 🎸 sitemap by @StanGirard in https://github.com/StanGirard/quivr/pull/1483

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.96...v0.0.97

## 0.0.96 (2023-10-24)

## What's Changed

- feat: validate email and required question with react-hook-form by @matthieujacq in https://github.com/StanGirard/quivr/pull/1463
- feat: fetch security questions from CMS by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1464
- feat: POST on /contact to contact quivr team by email by @matthieujacq in https://github.com/StanGirard/quivr/pull/1466
- feat: setup premium feature backend by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1467
- feat: add sponsor and blog links by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1472
- feat: contact sales submission by @matthieujacq in https://github.com/StanGirard/quivr/pull/1473
- feat(homepage): add analytics by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1474
- fix: make use cases clickable by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1475
- fix: update blog link position by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1476

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.95...v0.0.96

## 0.0.95 (2023-10-23)

## What's Changed

- feat: homepage first section by @matthieujacq in https://github.com/StanGirard/quivr/pull/1439
- fix: show a flat gradient in the UseCase section by @matthieujacq in https://github.com/StanGirard/quivr/pull/1440
- style: update testimonials display by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1441
- feat: fetch homepage data from CMS by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1452
- feat: contact sales page (front layout) by @matthieujacq in https://github.com/StanGirard/quivr/pull/1451
- Fix: Improved Text Visibility in Dark Mode in OnboardingQuestion Component by @Dev-Dz27 in https://github.com/StanGirard/quivr/pull/1456
- feat(cms): added single video demo by @StanGirard in https://github.com/StanGirard/quivr/pull/1459
- feat: fetch demo video from CMS by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1461
- feat: Contact form component by @matthieujacq in https://github.com/StanGirard/quivr/pull/1453

## New Contributors

- @Dev-Dz27 made their first contribution in https://github.com/StanGirard/quivr/pull/1456

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.94...v0.0.95

## 0.0.94 (2023-10-19)

## What's Changed

- feat: Homepage demo section by @matthieujacq in https://github.com/StanGirard/quivr/pull/1420
- feat: add security section by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1423
- feat: HomePage new footer by @matthieujacq in https://github.com/StanGirard/quivr/pull/1425
- feat: add testimonials section by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1427
- refactor(backend): cleaning dead and unused code by @StanGirard in https://github.com/StanGirard/quivr/pull/1432

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.93...v0.0.94

## 0.0.93 (2023-10-17)

## What's Changed

- feat: add use cases to homepage by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1415
- feat: Homepage screen 1 content by @matthieujacq in https://github.com/StanGirard/quivr/pull/1414

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.92...v0.0.93

## 0.0.92 (2023-10-17)

## What's Changed

- feat: add a cron to remove onboarding more than 7 days by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1397
- feat: 🖼️ new homepage background by @matthieujacq in https://github.com/StanGirard/quivr/pull/1395

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.91...v0.0.92

## 0.0.91 (2023-10-12)

## What's Changed

- feat: track onboarding events by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1388
- fix(user identity): User identity dict has no attribute user_id and open_api_key by @HamzaKhalidDhillon in https://github.com/StanGirard/quivr/pull/1351
- feat: new homepage header by @matthieujacq in https://github.com/StanGirard/quivr/pull/1382
- feat(onboarding): add suggested questions answer by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1390

## New Contributors

- @HamzaKhalidDhillon made their first contribution in https://github.com/StanGirard/quivr/pull/1351

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.90...v0.0.91

## 0.0.90 (2023-10-11)

## What's Changed

- feat: add Welcome chat by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1365
- feat: handle suggestion click by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1368
- refactor: Enable linting on login+signup page and hooks by @matthieujacq in https://github.com/StanGirard/quivr/pull/1369
- feat: finish onboarding step on first upload or crawl by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1373
- feat: add create_user_onboarding_function by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1383
- feat: remove user onboarding on complete by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1387

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.89...v0.0.90

## 0.0.89 (2023-10-09)

## What's Changed

- feat: enable CSP in all environments (local/preview/prod) by @matthieujacq in https://github.com/StanGirard/quivr/pull/1334
- feat: enhance user page UI by @nguernse in https://github.com/StanGirard/quivr/pull/1319
- feat: update onboarding steps by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1337
- feat: add onboarding_a column to onboarding table by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1340
- fix(question): fixed with user_settings by @StanGirard in https://github.com/StanGirard/quivr/pull/1349
- FIX tables.sql - missing ; breaks SQL queries. by @stanrb in https://github.com/StanGirard/quivr/pull/1348
- feat: ⚙️🐞 configure debugger for the backend by @matthieujacq in https://github.com/StanGirard/quivr/pull/1345
- test: add chat e2e tests by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1344
- feat: configure CSP for self-hosting and multiple ports in dev mode by @matthieujacq in https://github.com/StanGirard/quivr/pull/1364

## New Contributors

- @stanrb made their first contribution in https://github.com/StanGirard/quivr/pull/1348

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.88...v0.0.89

## 0.0.88 (2023-10-05)

## What's Changed

- fix: wrap parsing with try catch statements by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1321
- fix: 🐛 ↕️ 📱height now matches mobile height by @matthieujacq in https://github.com/StanGirard/quivr/pull/1323
- feat: add onboarding step 3 by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1324
- feat: restructure the sidebar of the brains management page by @matthieujacq in https://github.com/StanGirard/quivr/pull/1325
- feat: add onboarding table by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1327
- feat: update onboarding controller and fix typo by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1333

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.87...v0.0.88

## 0.0.87 (2023-10-03)

## What's Changed

- feat: ↕️ maximize brains management page by @matthieujacq in https://github.com/StanGirard/quivr/pull/1308
- refactor: ♻️ ContentSecurityPolicy as an object by @matthieujacq in https://github.com/StanGirard/quivr/pull/1312
- fix: replace next/image's layout deprecated attribute by @matthieujacq in https://github.com/StanGirard/quivr/pull/1313
- fix: remove undesired outlines around divs by @matthieujacq in https://github.com/StanGirard/quivr/pull/1316
- feat(onboarding): add step 2 by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1314
- feat(onboarding): add questions suggestions layout by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1318

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.86...v0.0.87

## 0.0.86 (2023-10-03)

## What's Changed

- fix(script): added percent before list of emails by @StanGirard in https://github.com/StanGirard/quivr/pull/1284
- feat: improve app ux by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1281
- test: add e2e for crawling by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1288
- feat(user_settings): increased by @StanGirard in https://github.com/StanGirard/quivr/pull/1291
- fix: prevent fetch when user is not logged in by @nguernse in https://github.com/StanGirard/quivr/pull/1293
- fix(Select): fix JSX typo by @nguernse in https://github.com/StanGirard/quivr/pull/1292
- feat: ✨ responsive sidebar by @matthieujacq in https://github.com/StanGirard/quivr/pull/1279
- fix: 🐛 Sidebar content should not hide the sidebar footer by @matthieujacq in https://github.com/StanGirard/quivr/pull/1298
- feat: allow users to turn private brain to public by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1300
- feat: add onboarding first step by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1303
- feat: update chat list on new chat first message by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1305
- feat: ↕️ Maximise chat window by @matthieujacq in https://github.com/StanGirard/quivr/pull/1301

## New Contributors

- @nguernse made their first contribution in https://github.com/StanGirard/quivr/pull/1293

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.85...v0.0.86

## 0.0.84 (2023-09-28)

## What's Changed

- feat: 👤 Implement gravatar by @matthieujacq in https://github.com/StanGirard/quivr/pull/1268
- feat: improve knowledge feed process ux by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1274
- fix: 🔒️ add gravatar.com to the content security policy by @matthieujacq in https://github.com/StanGirard/quivr/pull/1273
- feat(chatPage): update ui add new feed component by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1275
- feat: knowledge tab add button by @gozineb in https://github.com/StanGirard/quivr/pull/1277
- fix(brains): get brains on local by @B0rrA in https://github.com/StanGirard/quivr/pull/1272

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.83...v0.0.84

## 0.0.83 (2023-09-27)

## What's Changed

- feat(nav): 🚚 Move Brain and User buttons to the sidebar in the chat by @matthieujacq in https://github.com/StanGirard/quivr/pull/1262
- feat: save last chat config and make it default one by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1266
- style: improve upload ux by @Rahil2507 in https://github.com/StanGirard/quivr/pull/1259
- fix(docs): add prerequisites section in step 2 by @JvSdv in https://github.com/StanGirard/quivr/pull/1149
- style: improve ui by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1263

## New Contributors

- @Rahil2507 made their first contribution in https://github.com/StanGirard/quivr/pull/1259
- @JvSdv made their first contribution in https://github.com/StanGirard/quivr/pull/1149

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.82...v0.0.83

## 0.0.82 (2023-09-26)

## What's Changed

- fix(RBAC): skip validation for unplug by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1264

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.81...v0.0.82

## 0.0.81 (2023-09-26)

## What's Changed

- feat: activate public brain subscription by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1241
- feat(publicBrain): disable subscribe button when already subscribed and refetch brains list by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1246
- feat: knowledge tab list by @gozineb in https://github.com/StanGirard/quivr/pull/1222
- fix(preview): fixed a few bugs unchecked by @StanGirard in https://github.com/StanGirard/quivr/pull/1247
- feat: add last_update field to brain table by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1252
- fix: urls to avoid 307 by @gozineb in https://github.com/StanGirard/quivr/pull/1253
- feat: allow user to unsubscribe from a brain by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1254
- feat(user): 🚚 Move language and theme buttons to the User page by @matthieujacq in https://github.com/StanGirard/quivr/pull/1256
- Update next.config.js by @riccardolinares in https://github.com/StanGirard/quivr/pull/1251
- feat: allow setting public brain status to private by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1258

## New Contributors

- @matthieujacq made their first contribution in https://github.com/StanGirard/quivr/pull/1256
- @riccardolinares made their first contribution in https://github.com/StanGirard/quivr/pull/1251

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.80...v0.0.81

## 0.0.80 (2023-09-21)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.79...v0.0.80

## 0.0.79 (2023-09-21)

## What's Changed

- fix(gpt-3.5-instruct): bug and new version of node by @StanGirard in https://github.com/StanGirard/quivr/pull/1228
- feat: display brain status on settings page by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1221
- feat(upload): changed icons by @StanGirard in https://github.com/StanGirard/quivr/pull/1233
- Feat/paperclip by @StanGirard in https://github.com/StanGirard/quivr/pull/1234
- fix(brain_size): increased size by @StanGirard in https://github.com/StanGirard/quivr/pull/1235
- feat(strapi): added first draft by @StanGirard in https://github.com/StanGirard/quivr/pull/1237
- feat: add public brain page by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1230
- feat: count public brains number of subscribers by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1236

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.78...v0.0.79

## 0.0.78 (2023-09-20)

## What's Changed

- feat: add public brain creation by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1218
- feat: get files from storage by @gozineb in https://github.com/StanGirard/quivr/pull/1205

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.77...v0.0.78

## 0.0.77 (2023-09-19)

## What's Changed

- feat(memory): optimisation by @StanGirard in https://github.com/StanGirard/quivr/pull/1214

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.76...v0.0.77

## 0.0.76 (2023-09-19)

## What's Changed

- chore(theodo): added trigger function for theodo by @StanGirard in https://github.com/StanGirard/quivr/pull/1195
- feat: add tooltip on upload card toggle button by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1197
- feat(analytics): add google analytics by @gozineb in https://github.com/StanGirard/quivr/pull/1147
- Feat/theodo gpt4 by @StanGirard in https://github.com/StanGirard/quivr/pull/1198
- fix(chats): now in order and with a little bonus ;) by @StanGirard in https://github.com/StanGirard/quivr/pull/1200
- fix: fix some bugs by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1201
- feat(question): now not rephrasing question when passed to answering llm by @StanGirard in https://github.com/StanGirard/quivr/pull/1202

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.75...v0.0.76

## 0.0.75 (2023-09-18)

## What's Changed

- feat(frontend): responsiveness by @StanGirard in https://github.com/StanGirard/quivr/pull/1174
- feat(upload): changed to task by @StanGirard in https://github.com/StanGirard/quivr/pull/1178
- fix(qa_base): asign max_token to llm by @B0rrA in https://github.com/StanGirard/quivr/pull/1179
- feat(perf): increased perf embedding and search for files by @StanGirard in https://github.com/StanGirard/quivr/pull/1182
- fix(usersettings): Fix bugs with user settings in the back always gpt-3.5-turbo by @StanGirard in https://github.com/StanGirard/quivr/pull/1183
- feat(notificatins): higher refresh rate by @StanGirard in https://github.com/StanGirard/quivr/pull/1184
- style(notifications): improve the messages for the notifications by @StanGirard in https://github.com/StanGirard/quivr/pull/1185
- fix(notifications): dead notifications that are still present long after by @StanGirard in https://github.com/StanGirard/quivr/pull/1186
- fix(notifications): greater than 5 minutes ago not less by @StanGirard in https://github.com/StanGirard/quivr/pull/1187
- feat(aws): improved size by @StanGirard in https://github.com/StanGirard/quivr/pull/1188
- feat(concurrency): added concurrency for increased performance by @StanGirard in https://github.com/StanGirard/quivr/pull/1189
- feat(prompt): improved answer readability with markdown and aerataed by @StanGirard in https://github.com/StanGirard/quivr/pull/1190
- fix(notification): information now displayed on the right by @StanGirard in https://github.com/StanGirard/quivr/pull/1191
- feat(chat): added copy feature to message by @StanGirard in https://github.com/StanGirard/quivr/pull/1192
- feat(e2e): add playright config and createBrain e2e test by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1177

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.74...v0.0.75

## 0.0.74 (2023-09-14)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.73...v0.0.74

## 0.0.73 (2023-09-14)

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.72...v0.0.73

## 0.0.72 (2023-09-14)

## What's Changed

- feat(file-system): added queue and filesystem by @StanGirard in https://github.com/StanGirard/quivr/pull/1159
- fix(migration): removed by @StanGirard in https://github.com/StanGirard/quivr/pull/1170

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.71...v0.0.72

## 0.0.71 (2023-09-14)

## What's Changed

- feat: the good user management by @StanGirard in https://github.com/StanGirard/quivr/pull/1158
- feat: add knowledge tab on brains settings page by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1163
- feat: update header and improve ux by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1164
- feat: submit upload on Enter by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1160
- feat: make error messages more clear by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1166

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.70...v0.0.71

## 0.0.70 (2023-09-12)

## What's Changed

- feat: add notifications components by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1148
- feat: add polling for pending notifications by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1152
- fix(selectedBrain): prevent picking brainId from local storage by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1154
- feat: update isValidUrl function by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1155

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.69...v0.0.70

## 0.0.69 (2023-09-08)

## What's Changed

- feat: add multiple upload and crawl in parallel by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1118
- feat: allow user to chat while feed process is pending by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1120
- feat: add notifications table, and push notification on upload and crawl by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1125
- feat: merge chat history with chat notifications by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1127
- feat(feedBrain): add request pending message #1135 by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1136
- fix: update crawl and upload endpoints by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1142
- make docker compose command more flexible by @thehunmonkgroup in https://github.com/StanGirard/quivr/pull/1139

## New Contributors

- @thehunmonkgroup made their first contribution in https://github.com/StanGirard/quivr/pull/1139

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.68...v0.0.69

## 0.0.68 (2023-09-06)

## What's Changed

- feat(liteLLM): Add support for Azure OpenAI, Palm, Claude-2, Llama2, CodeLlama (100+LLMs) by @ishaan-jaff in https://github.com/StanGirard/quivr/pull/1097
- feat(crawler): add multiple urls support by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1112
- fix(csp): add growthbook to csp headers by @gozineb in https://github.com/StanGirard/quivr/pull/1117

## New Contributors

- @ishaan-jaff made their first contribution in https://github.com/StanGirard/quivr/pull/1097

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.67...v0.0.68

## 0.0.67 (2023-09-05)

## What's Changed

- feat: add FeedBrainInput component by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1101
- feat: add <Feed /> component in chat page by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1103
- 🚑 fix feature flags -> add it back to context by @gozineb in https://github.com/StanGirard/quivr/pull/1106

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.66...v0.0.67

## 0.0.66 (2023-09-04)

## What's Changed

- fix(prod): add url api.openai to CSP headers by @gozineb in https://github.com/StanGirard/quivr/pull/1077
- feat: change share brain button logic by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1078
- fix(brainsSettings): handle nullish value from api call by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1080
- fix: update hash routing logic by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1082
- fix: fix hash redirection by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1085
- feat: add tanstack query and optimistic fetch on brains settings page by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1087
- docs: writeup for running quivr fully locally by @mvda in https://github.com/StanGirard/quivr/pull/1096
- fix: prevent submit on share button click by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1098

## New Contributors

- @mvda made their first contribution in https://github.com/StanGirard/quivr/pull/1096

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.65...v0.0.66

## 0.0.65 (2023-08-31)

## What's Changed

- feat: make chatlist scrollable by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1064
- feat(newBrain): update data validation logic and add \* on required fields by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1065
- feat(shareBrain): prevent re inviting users with access by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1063
- feat(chatMessage): update attributes display by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1067
- fix: fix minor bugs by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1070
- feat: remove mic button by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1075
- feat: truncate long chat name by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1076

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.64...v0.0.65

## 0.0.64 (2023-08-30)

## What's Changed

- feat: add prompt trigger through # by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1023
- feat: add headless question tracking by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1051
- feat: update header and remove prompt / brain on backspace by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1052
- feat: handle new chat button click by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1054
- feat: add name missing error in new brain modal by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1055
- feat: validate api key before saving by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1057
- feat: track prompt and brain changes by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1058
- feat(chat): update chat input placeholder by @gozineb in https://github.com/StanGirard/quivr/pull/1060
- fix(invitationPage): avoid multiple re-rendering by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1062

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.63...v0.0.64

## 0.0.63 (2023-08-27)

## What's Changed

- fix(dockerfile): backend Dockerfile exit code 1 by @pat266 in https://github.com/StanGirard/quivr/pull/1032
- test(backend): skip failing tests by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1036
- feat(messagesList): auto scroll on new message by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1040
- test: unskip `qa_headless.py` linter tests by @mamadoudicko in https://github.com/StanGirard/quivr/pull/1041
- feat: add 2 save buttons on Brain management tab by @ChloeMouret in https://github.com/StanGirard/quivr/pull/1039
- feat(brain): add endpoint to return context to question by @ZongZiWang in https://github.com/StanGirard/quivr/pull/1044
- fix: English grammar translation.json by @elie222 in https://github.com/StanGirard/quivr/pull/1046

## New Contributors

- @pat266 made their first contribution in https://github.com/StanGirard/quivr/pull/1032
- @elie222 made their first contribution in https://github.com/StanGirard/quivr/pull/1046

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.62...v0.0.63

## 0.0.62 (2023-08-25)

## What's Changed

- feat: Remove chat id from chat list by @ChloeMouret in https://github.com/StanGirard/quivr/pull/1031
- fix(analytics): june debug for real by @gozineb in https://github.com/StanGirard/quivr/pull/1033

**Full Changelog**: https://github.com/StanGirard/quivr/compare/v0.0.61...v0.0.62

## [0.0.61](https://github.com/StanGirard/quivr/compare/v0.0.60...v0.0.61) (2023-08-23)

### Features

- add brain prompt overwritting from chat ([#1012](https://github.com/StanGirard/quivr/issues/1012)) ([b967c2d](https://github.com/StanGirard/quivr/commit/b967c2d2d60b93f9142fe2afd04fb9422adcc2be))
- **backend:** adds python code parsing ([#1003](https://github.com/StanGirard/quivr/issues/1003)) ([a626b84](https://github.com/StanGirard/quivr/commit/a626b84b96c7b40904960e039f72ff042148a240))
- **prompts:** add public prompts to SQL db ([#1014](https://github.com/StanGirard/quivr/issues/1014)) ([4b1f4b1](https://github.com/StanGirard/quivr/commit/4b1f4b141287d109794aa6015f83deb3882ac5cb))
- **translation:** Added Simplified Chinese translation，Fix pt-br not working ([#1011](https://github.com/StanGirard/quivr/issues/1011)) ([e328ab8](https://github.com/StanGirard/quivr/commit/e328ab81b30f2dd3dae7287e0351fdacd1c18133))

### Bug Fixes

- **Analytics:** no tags tracking for upload & crawl ([#1024](https://github.com/StanGirard/quivr/issues/1024)) ([2b74ebc](https://github.com/StanGirard/quivr/commit/2b74ebc1f099c4d12705d458fefad94120af9208))

## [0.0.60](https://github.com/StanGirard/quivr/compare/v0.0.59...v0.0.60) (2023-08-22)

### Features

- **chat:** add brain selection through mention input ([#969](https://github.com/StanGirard/quivr/issues/969)) ([8e94f22](https://github.com/StanGirard/quivr/commit/8e94f22782dd2255e8125fbb4b3718413ad4701e))

### Bug Fixes

- remove conflicts ([#998](https://github.com/StanGirard/quivr/issues/998)) ([f61b70a](https://github.com/StanGirard/quivr/commit/f61b70a34f6d24e6f343d31cc4aa63265bb1c218))
- update backend tests ([#992](https://github.com/StanGirard/quivr/issues/992)) ([5a3a6fe](https://github.com/StanGirard/quivr/commit/5a3a6fe370756783a204f2a62007f6cb23c7b202))

## [0.0.59](https://github.com/StanGirard/quivr/compare/v0.0.58...v0.0.59) (2023-08-20)

### Features

- **aws:** all in microservices ([b3a6231](https://github.com/StanGirard/quivr/commit/b3a6231274e5aea28675381ba6f7ba277228f5ac))
- **chat-service:** added task definition ([d001ec7](https://github.com/StanGirard/quivr/commit/d001ec70df3ccd5f3885b5f174e58f1b3238c433))
- **docker:** improved size image ([#978](https://github.com/StanGirard/quivr/issues/978)) ([aa623c4](https://github.com/StanGirard/quivr/commit/aa623c4039ba31928dd0934a682259c7762d2efa))
- **docker:** pushing image to github registry ([ad3dca3](https://github.com/StanGirard/quivr/commit/ad3dca3e2705b87a9c9c0b35f67773bcc182ae88))
- **gcr:** removed sha and put latest ([2b85a94](https://github.com/StanGirard/quivr/commit/2b85a94e8835861afd9c178b72e59d018d8b956f))
- **health:** added endpoint for services ([#989](https://github.com/StanGirard/quivr/issues/989)) ([ae7852e](https://github.com/StanGirard/quivr/commit/ae7852ec3f9d6e20b28c3b6fbc0d433d476395ea))
- **microservices:** split into 4 quivr to better handle long services ([#972](https://github.com/StanGirard/quivr/issues/972)) ([7281fd9](https://github.com/StanGirard/quivr/commit/7281fd905a24b8e4dad7214d7809b8856685fca8))
- **preview:** added crawl service to ci ([b7f9876](https://github.com/StanGirard/quivr/commit/b7f9876ce20a2c802ccfd7cff35de50ac2fd2226))
- **preview:** added preview ([#974](https://github.com/StanGirard/quivr/issues/974)) ([9eb25a4](https://github.com/StanGirard/quivr/commit/9eb25a4d1777b9fdbc1c4b93df0b51e8b28d3ae9))
- **preview:** added service upload ([#979](https://github.com/StanGirard/quivr/issues/979)) ([ce6b45e](https://github.com/StanGirard/quivr/commit/ce6b45e1ac8e9a3d21b7f56ad228351e34179e11))
- **refacto:** changed a bit of things to make better dx ([#984](https://github.com/StanGirard/quivr/issues/984)) ([d0370ab](https://github.com/StanGirard/quivr/commit/d0370ab499465ee1404d3c1d32878e8da3853441))
- **Unplug:** chatting without brain streaming ([#970](https://github.com/StanGirard/quivr/issues/970)) ([600ff1e](https://github.com/StanGirard/quivr/commit/600ff1ede02741c66853cc3e4e7f5001aaba3bc2))

### Bug Fixes

- **settings:** select proper brain model ([#943](https://github.com/StanGirard/quivr/issues/943)) ([3a44f54](https://github.com/StanGirard/quivr/commit/3a44f54d6b75581e3cbc8acf0c1c309c3273e63f))
- update backend tests ([#975](https://github.com/StanGirard/quivr/issues/975)) ([c746eb1](https://github.com/StanGirard/quivr/commit/c746eb18303945a1736c89427026b509f501e715))
- **windows:** removed unused start script ([#962](https://github.com/StanGirard/quivr/issues/962)) ([ad7ac15](https://github.com/StanGirard/quivr/commit/ad7ac1516d5c45c833c9e9ba6162012096372fa6))

## [0.0.57](https://github.com/StanGirard/quivr/compare/v0.0.56...v0.0.57) (2023-08-16)

### Features

- add brain missing message ([#958](https://github.com/StanGirard/quivr/issues/958)) ([f99f81d](https://github.com/StanGirard/quivr/commit/f99f81d10f9c768af00e38249763a252f8db16e3))
- change messages position ([#946](https://github.com/StanGirard/quivr/issues/946)) ([9235a84](https://github.com/StanGirard/quivr/commit/9235a848d12b96af346cc2cbb1ac50dc2f67b20c))
- update chat ui ([#907](https://github.com/StanGirard/quivr/issues/907)) ([80be40a](https://github.com/StanGirard/quivr/commit/80be40ad34d07b646d48d2aa0405a92b3de308d7))

### Bug Fixes

- **chat routes:** use brain model, temp, and token ([#902](https://github.com/StanGirard/quivr/issues/902)) ([59ddfb4](https://github.com/StanGirard/quivr/commit/59ddfb48823b56239fe7fc95133274a3bedf49da))
- **chatMessages:** Fix error on answering question ([#953](https://github.com/StanGirard/quivr/issues/953)) ([1fef9b0](https://github.com/StanGirard/quivr/commit/1fef9b078379c8991f6029c34ac10d4cbdc5a44d))
- **crawler:** using newspaper and fixed recursive by merging content ([#955](https://github.com/StanGirard/quivr/issues/955)) ([d7c5c79](https://github.com/StanGirard/quivr/commit/d7c5c79043827b2b0949f6fd6c508c4617dcf498))
- **translations:** pr portuguese translations ([#933](https://github.com/StanGirard/quivr/issues/933)) ([d80178a](https://github.com/StanGirard/quivr/commit/d80178a84802c35b2c13d3eef4d0438fd067da92))

## [0.0.56](https://github.com/StanGirard/quivr/compare/v0.0.55...v0.0.56) (2023-08-10)

### Bug Fixes

- **chat:** update data keys ([#923](https://github.com/StanGirard/quivr/issues/923)) ([21db719](https://github.com/StanGirard/quivr/commit/21db7197965f1cacd6595ae94d9017fc54d761c3))

## [0.0.55](https://github.com/StanGirard/quivr/compare/v0.0.54...v0.0.55) (2023-08-10)

### Features

- **chatMessages:** add brain_id and prompt_id columns ([#912](https://github.com/StanGirard/quivr/issues/912)) ([6e77732](https://github.com/StanGirard/quivr/commit/6e777327aaee7b9f35b20dcd00814f4acbaf448e))
- **invitation:** add translations ([#909](https://github.com/StanGirard/quivr/issues/909)) ([1360ce8](https://github.com/StanGirard/quivr/commit/1360ce801d8958defa5dd29a481e2e66ac6ae9ac))
- Russian language translation ([#903](https://github.com/StanGirard/quivr/issues/903)) ([672eec0](https://github.com/StanGirard/quivr/commit/672eec08bc7113e3f4c32a29ae86b2b879262d30))

## [0.0.54](https://github.com/StanGirard/quivr/compare/v0.0.53...v0.0.54) (2023-08-08)

### Features

- add new chat bar ([#896](https://github.com/StanGirard/quivr/issues/896)) ([69a73f5](https://github.com/StanGirard/quivr/commit/69a73f5d5ae58dca9c23c0d8751f8c7326c84f4c))
- add new chat page ([#890](https://github.com/StanGirard/quivr/issues/890)) ([c43e0c0](https://github.com/StanGirard/quivr/commit/c43e0c01c4ddcf0d97b9bb89784ff004fb7a0a79))
- deleting brains on brain manager page ([#893](https://github.com/StanGirard/quivr/issues/893)) ([71e142b](https://github.com/StanGirard/quivr/commit/71e142ba3c164e5f14959cd1fd5de38531779034))

### Bug Fixes

- **es:** spanish translations ([#895](https://github.com/StanGirard/quivr/issues/895)) ([69d0893](https://github.com/StanGirard/quivr/commit/69d08937de1540cf39a6462b4583b2c4c908d0af))
- **sentry:** some unhandled errors ([#894](https://github.com/StanGirard/quivr/issues/894)) ([9ba7241](https://github.com/StanGirard/quivr/commit/9ba724168eacf4b074ad062f2a58b637597335ba))

## [0.0.53](https://github.com/StanGirard/quivr/compare/v0.0.52...v0.0.53) (2023-08-07)

### Features

- **backend:** add custom prompt ([#885](https://github.com/StanGirard/quivr/issues/885)) ([61cd0a6](https://github.com/StanGirard/quivr/commit/61cd0a6bde989bc9f931f47967c3bbddc3b0446b))
- **fr:** added language ([#884](https://github.com/StanGirard/quivr/issues/884)) ([1160e16](https://github.com/StanGirard/quivr/commit/1160e160141f350a39ae4f73ff88ad79e1b1d874))
- gpt4 is not available for brains if there is no given openAiKey ([#850](https://github.com/StanGirard/quivr/issues/850)) ([e9ebeef](https://github.com/StanGirard/quivr/commit/e9ebeef72ae2dee40b6bdff58121f9f9e1814577))
- **qa:** improve code ([#886](https://github.com/StanGirard/quivr/issues/886)) ([7028505](https://github.com/StanGirard/quivr/commit/7028505571a8e1f8569a12b770b3ce99cd2ec4e0))

### Bug Fixes

- **i18n:** update tests for french and spanish ([#878](https://github.com/StanGirard/quivr/issues/878)) ([b0514d6](https://github.com/StanGirard/quivr/commit/b0514d6149d474747de642d12454f6b511a1f947))

## [0.0.52](https://github.com/StanGirard/quivr/compare/v0.0.51...v0.0.52) (2023-08-07)

### Features

- add custom prompt fields on brain setting pages ([#837](https://github.com/StanGirard/quivr/issues/837)) ([99a3fa9](https://github.com/StanGirard/quivr/commit/99a3fa9b296520a71028194e21bc808a2ec208a0))
- add public prompts picker ([#841](https://github.com/StanGirard/quivr/issues/841)) ([b3fb8fc](https://github.com/StanGirard/quivr/commit/b3fb8fc3bc2d71a72e73b4f0aa30c84255a77fc0))
- remove private prompts on related brain delete ([#842](https://github.com/StanGirard/quivr/issues/842)) ([4c15fe2](https://github.com/StanGirard/quivr/commit/4c15fe2bfde7a2fdc59c299ef668f1ba0cd8ffa8))

### Bug Fixes

- **pg-database:** by default variable is not implemented ([#848](https://github.com/StanGirard/quivr/issues/848)) ([69e2c28](https://github.com/StanGirard/quivr/commit/69e2c289e5a6e4cfd6b7187a3c4fda5c538d5d35))
- remove typo ([#853](https://github.com/StanGirard/quivr/issues/853)) ([5496e9d](https://github.com/StanGirard/quivr/commit/5496e9d738a1f80f11b6c8fa8606960abcbcd06d))

### Performance Improvements

- **deps:** removed ([#873](https://github.com/StanGirard/quivr/issues/873)) ([10d4d65](https://github.com/StanGirard/quivr/commit/10d4d65c1e203aaae1069395ed5066fbfc9c7715))

## [0.0.51](https://github.com/StanGirard/quivr/compare/v0.0.50...v0.0.51) (2023-08-03)

### Features

- **backend:** implement brain-prompt link ([#831](https://github.com/StanGirard/quivr/issues/831)) ([4ca6c66](https://github.com/StanGirard/quivr/commit/4ca6c667da3d5daf0339c65f077c8956c7ef42e8))
- **prompt:** add prompt table, entity and repository ([#823](https://github.com/StanGirard/quivr/issues/823)) ([e3b6114](https://github.com/StanGirard/quivr/commit/e3b6114248ee04a9dc6b93093256d82324672925))

### Bug Fixes

- **chat routes:** HTTPException import correction ([#833](https://github.com/StanGirard/quivr/issues/833)) ([68f03b2](https://github.com/StanGirard/quivr/commit/68f03b2416f5b49e9f8e72c5b1c91754792a1233))
- **chats:** delete chats from correct table ([#834](https://github.com/StanGirard/quivr/issues/834)) ([659e585](https://github.com/StanGirard/quivr/commit/659e585145ea0aa8bf88ecc48d31e0b65098a729))
- **env:** added pg database url default value to none ([23f50ec](https://github.com/StanGirard/quivr/commit/23f50ec3a37af453f1b8b69592d1a640189d50e8))

## [0.0.50](https://github.com/StanGirard/quivr/compare/v0.0.49...v0.0.50) (2023-08-02)

### Features

- Introduce repository pattern to prepare adding other database providers ([#646](https://github.com/StanGirard/quivr/issues/646)) ([303ba72](https://github.com/StanGirard/quivr/commit/303ba72028d349196b78cc07db627115ec0aff90))
- **prompt:** added instructions in standalone question & a bit more things ([#826](https://github.com/StanGirard/quivr/issues/826)) ([c217979](https://github.com/StanGirard/quivr/commit/c21797905d7d57dab73f9b7047da1a50aae37b9b))

## [0.0.49](https://github.com/StanGirard/quivr/compare/v0.0.48...v0.0.49) (2023-08-01)

### Features

- add chat config modal ([#807](https://github.com/StanGirard/quivr/issues/807)) ([d018ab6](https://github.com/StanGirard/quivr/commit/d018ab6a9334b45b86e0c7fed3a552f5cb202523))

### Bug Fixes

- bugs ([#818](https://github.com/StanGirard/quivr/issues/818)) ([edcbb30](https://github.com/StanGirard/quivr/commit/edcbb30e97535013b61d5a94bb4204d030cba2f2))

## [0.0.48](https://github.com/StanGirard/quivr/compare/v0.0.47...v0.0.48) (2023-08-01)

### Bug Fixes

- **openai:** user key now used for llm model ([c01433c](https://github.com/StanGirard/quivr/commit/c01433c84194e1d155ad3917de58257d24c30c38))

## [0.0.47](https://github.com/StanGirard/quivr/compare/v0.0.46...v0.0.47) (2023-08-01)

### Features

- add user level open ai key management ([#805](https://github.com/StanGirard/quivr/issues/805)) ([7532b55](https://github.com/StanGirard/quivr/commit/7532b558c74962e5916b951235e8578cc8e882a2))
- **chat:** added streaming ([#808](https://github.com/StanGirard/quivr/issues/808)) ([3166d08](https://github.com/StanGirard/quivr/commit/3166d089ee82730882c26454bd110a3dfae067c9))
- **llm:** removing all llms to prepare for genoss ([#804](https://github.com/StanGirard/quivr/issues/804)) ([db40f3c](https://github.com/StanGirard/quivr/commit/db40f3cccd596f4337823e0306e66224d5e1c8c9))

## [0.0.46](https://github.com/StanGirard/quivr/compare/v0.0.45...v0.0.46) (2023-07-31)

### Features

- **aws:** increased numer of replicas to 10 ([9809ef4](https://github.com/StanGirard/quivr/commit/9809ef4119a2351b78217c73c545b7e327676135))
- **aws:** increased size ([56f254a](https://github.com/StanGirard/quivr/commit/56f254a050fcc3b9ee073318bd566e03675658cd))

### Bug Fixes

- **frontend:** correctly display document information in explore view details ([#781](https://github.com/StanGirard/quivr/issues/781)) ([87c5e58](https://github.com/StanGirard/quivr/commit/87c5e582a2579ebb68f272cb62175dfa6f2e6dc8))
- Toast message hidden under the footer ([#761](https://github.com/StanGirard/quivr/issues/761)) ([3e8ed46](https://github.com/StanGirard/quivr/commit/3e8ed463173659ebe599602e97c2d11191144ecb))
- udpate migration script doc ([#793](https://github.com/StanGirard/quivr/issues/793)) ([a609c01](https://github.com/StanGirard/quivr/commit/a609c01aa8fab10e74eed64edd795c56bece1fdb))

## [0.0.45](https://github.com/StanGirard/quivr/compare/v0.0.44...v0.0.45) (2023-07-27)

### Bug Fixes

- **release-please:** use personal token to be able to trigger release ([#789](https://github.com/StanGirard/quivr/issues/789)) ([2fcff0b](https://github.com/StanGirard/quivr/commit/2fcff0bedab3a53cb3dc395c0e362edb2962aaa7))

## [0.0.44](https://github.com/StanGirard/quivr/compare/v0.0.43...v0.0.44) (2023-07-27)

### Features

- **pr-title:** added pr-title checlk ([b11b2d8](https://github.com/StanGirard/quivr/commit/b11b2d8658fec3940bb0c0280124cbdd77d8d74b))

### Bug Fixes

- **release-please:** fixed actions ([16114b2](https://github.com/StanGirard/quivr/commit/16114b2c5271ab299b7e84f5a9e552dab58cd211))

## [0.0.43](https://github.com/StanGirard/quivr/compare/v0.0.42...v0.0.43) (2023-07-26)

### Features

- **workflow:** added release please ([e6ba9e8](https://github.com/StanGirard/quivr/commit/e6ba9e80f48a1d8822c99e5b77e064dc2b18e718))
