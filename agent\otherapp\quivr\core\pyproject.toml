[project]
name = "quivr-core"
version = "0.0.33"
description = "Quivr core RAG package"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
dependencies = [
    "pydantic>=2.8.2",
    "langchain-core>=0.3,<0.4",
    "langchain>=0.3.9,<0.4",
    "langgraph>=0.2.38,<0.3",
    "httpx>=0.27.0",
    "rich>=13.7.1",
    "tiktoken>=0.7.0",
    "aiofiles>=23.1.0",
    "langchain-openai>=0.3.0",
    "langchain-cohere>=0.1.0",
    "langchain-community>=0.3,<0.4",
    "langchain-anthropic>=0.1.23",
    "types-pyyaml>=6.0.12.20240808",
    "transformers[sentencepiece]>=4.44.2",
    "faiss-cpu>=1.8.0.post1",
    "rapidfuzz>=3.10.1",
    "markupsafe>=2.1.5",
    "megaparse-sdk>=0.1.11",
    "langchain-mistralai>=0.2.3",
    "langchain-google-genai>=2.1.3",
    "fasttext-langdetect>=1.0.5",
    "langfuse>=2.57.0",
    "langchain-groq>=0.3.2",
]
readme = "README.md"
requires-python = ">= 3.11"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = [
    "mypy>=1.11.1",
    "pre-commit>=3.8.0",
    "ipykernel>=6.29.5",
    "ruff>=0.6.1",
    "flake8>=7.1.1",
    "flake8-black>=0.3.6",
    "pytest-asyncio>=0.23.8",
    "pytest>=8.3.2",
    "pytest-xdist>=3.6.1",
    "pytest-benchmark>=4.0.0",
]

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["quivr_core"]

[tool.pytest.ini_options]
addopts = "--tb=short -ra -v"
filterwarnings = ["ignore::DeprecationWarning"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "base: these tests require quivr-core with extra `base` to be installed",
    "tika: these tests require a tika server to be running",
    "unstructured: these tests require `unstructured` dependency",
]

[[tool.mypy.overrides]]
module = "yaml"
ignore_missing_imports = true
