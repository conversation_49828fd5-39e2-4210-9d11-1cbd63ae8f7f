# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

aiofiles==23.2.1
    # via chainlit
    # via quivr-core
aiohappyeyeballs==2.4.3
    # via aiohttp
aiohttp==3.10.10
    # via langchain
    # via langchain-community
aiosignal==1.3.1
    # via aiohttp
alembic==1.13.3
    # via mlflow
aniso8601==9.0.1
    # via graphene
annotated-types==0.7.0
    # via pydantic
anthropic==0.40.0
    # via langchain-anthropic
anyio==4.6.2.post1
    # via anthropic
    # via asyncer
    # via httpx
    # via openai
    # via starlette
    # via watchfiles
asyncer==0.0.7
    # via chainlit
attrs==23.2.0
    # via aiohttp
    # via jsonschema
    # via referencing
    # via sagemaker
bidict==0.23.1
    # via python-socketio
blinker==1.8.2
    # via flask
boto3==1.35.42
    # via cohere
    # via sagemaker
    # via sagemaker-core
    # via sagemaker-mlflow
botocore==1.35.42
    # via boto3
    # via s3transfer
cachetools==5.5.0
    # via google-auth
    # via mlflow-skinny
certifi==2024.8.30
    # via httpcore
    # via httpx
    # via requests
chainlit==1.3.2
charset-normalizer==3.4.0
    # via requests
chevron==0.14.0
    # via literalai
click==8.1.7
    # via chainlit
    # via flask
    # via mlflow-skinny
    # via uvicorn
cloudpickle==2.2.1
    # via mlflow-skinny
    # via sagemaker
cohere==5.11.0
    # via langchain-cohere
contourpy==1.3.0
    # via matplotlib
cycler==0.12.1
    # via matplotlib
databricks-sdk==0.34.0
    # via mlflow-skinny
dataclasses-json==0.6.7
    # via chainlit
    # via langchain-community
defusedxml==0.7.1
    # via langchain-anthropic
deprecated==1.2.14
    # via opentelemetry-api
    # via opentelemetry-exporter-otlp-proto-grpc
    # via opentelemetry-exporter-otlp-proto-http
    # via opentelemetry-semantic-conventions
dill==0.3.9
    # via multiprocess
    # via pathos
distro==1.9.0
    # via anthropic
    # via openai
docker==7.1.0
    # via mlflow
    # via sagemaker
faiss-cpu==1.9.0
    # via quivr-core
fastapi==0.115.5
    # via chainlit
fastavro==1.9.7
    # via cohere
filelock==3.16.1
    # via huggingface-hub
    # via transformers
filetype==1.2.0
    # via chainlit
flask==3.0.3
    # via mlflow
fonttools==4.54.1
    # via matplotlib
frozenlist==1.4.1
    # via aiohttp
    # via aiosignal
fsspec==2024.9.0
    # via huggingface-hub
gitdb==4.0.11
    # via gitpython
gitpython==3.1.43
    # via mlflow-skinny
google-auth==2.35.0
    # via databricks-sdk
google-pasta==0.2.0
    # via sagemaker
googleapis-common-protos==1.65.0
    # via opentelemetry-exporter-otlp-proto-grpc
    # via opentelemetry-exporter-otlp-proto-http
graphene==3.3
    # via mlflow
graphql-core==3.2.5
    # via graphene
    # via graphql-relay
graphql-relay==3.2.0
    # via graphene
grpcio==1.67.0
    # via opentelemetry-exporter-otlp-proto-grpc
gunicorn==23.0.0
    # via mlflow
h11==0.14.0
    # via httpcore
    # via uvicorn
    # via wsproto
httpcore==1.0.6
    # via httpx
httpx==0.27.2
    # via anthropic
    # via chainlit
    # via cohere
    # via langchain-mistralai
    # via langgraph-sdk
    # via langsmith
    # via literalai
    # via megaparse-sdk
    # via openai
    # via quivr-core
httpx-sse==0.4.0
    # via cohere
    # via langchain-community
    # via langchain-mistralai
huggingface-hub==0.25.2
    # via tokenizers
    # via transformers
idna==3.10
    # via anyio
    # via httpx
    # via requests
    # via yarl
importlib-metadata==6.11.0
    # via mlflow-skinny
    # via opentelemetry-api
    # via sagemaker
    # via sagemaker-core
itsdangerous==2.2.0
    # via flask
jinja2==3.1.4
    # via flask
    # via mlflow
jiter==0.6.1
    # via anthropic
    # via openai
jmespath==1.0.1
    # via boto3
    # via botocore
joblib==1.4.2
    # via scikit-learn
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.23.0
    # via sagemaker
    # via sagemaker-core
jsonschema-specifications==2024.10.1
    # via jsonschema
kiwisolver==1.4.7
    # via matplotlib
langchain==0.3.9
    # via langchain-community
    # via quivr-core
langchain-anthropic==0.3.0
    # via quivr-core
langchain-cohere==0.3.3
    # via quivr-core
langchain-community==0.3.9
    # via langchain-experimental
    # via quivr-core
langchain-core==0.3.21
    # via langchain
    # via langchain-anthropic
    # via langchain-cohere
    # via langchain-community
    # via langchain-experimental
    # via langchain-mistralai
    # via langchain-openai
    # via langchain-text-splitters
    # via langgraph
    # via langgraph-checkpoint
    # via quivr-core
langchain-experimental==0.3.3
    # via langchain-cohere
langchain-mistralai==0.2.3
    # via quivr-core
langchain-openai==0.2.11
    # via quivr-core
langchain-text-splitters==0.3.2
    # via langchain
langgraph==0.2.56
    # via quivr-core
langgraph-checkpoint==2.0.8
    # via langgraph
langgraph-sdk==0.1.43
    # via langgraph
langsmith==0.1.135
    # via langchain
    # via langchain-community
    # via langchain-core
lazify==0.4.0
    # via chainlit
literalai==0.0.623
    # via chainlit
loguru==0.7.2
    # via megaparse-sdk
mako==1.3.5
    # via alembic
markdown==3.7
    # via mlflow
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.1
    # via jinja2
    # via mako
    # via quivr-core
    # via werkzeug
marshmallow==3.22.0
    # via dataclasses-json
matplotlib==3.9.2
    # via mlflow
mdurl==0.1.2
    # via markdown-it-py
megaparse-sdk==0.1.10
    # via quivr-core
mlflow==2.17.0
    # via sagemaker-mlflow
mlflow-skinny==2.17.0
    # via mlflow
mock==4.0.3
    # via sagemaker-core
msgpack==1.1.0
    # via langgraph-checkpoint
multidict==6.1.0
    # via aiohttp
    # via yarl
multiprocess==0.70.17
    # via pathos
mypy-extensions==1.0.0
    # via typing-inspect
nats-py==2.9.0
    # via megaparse-sdk
nest-asyncio==1.6.0
    # via chainlit
numpy==1.26.4
    # via chainlit
    # via contourpy
    # via faiss-cpu
    # via langchain
    # via langchain-community
    # via matplotlib
    # via mlflow
    # via pandas
    # via pyarrow
    # via sagemaker
    # via scikit-learn
    # via scipy
    # via transformers
openai==1.56.2
    # via langchain-openai
opentelemetry-api==1.27.0
    # via mlflow-skinny
    # via opentelemetry-exporter-otlp-proto-grpc
    # via opentelemetry-exporter-otlp-proto-http
    # via opentelemetry-instrumentation
    # via opentelemetry-sdk
    # via opentelemetry-semantic-conventions
    # via uptrace
opentelemetry-exporter-otlp==1.27.0
    # via uptrace
opentelemetry-exporter-otlp-proto-common==1.27.0
    # via opentelemetry-exporter-otlp-proto-grpc
    # via opentelemetry-exporter-otlp-proto-http
opentelemetry-exporter-otlp-proto-grpc==1.27.0
    # via opentelemetry-exporter-otlp
opentelemetry-exporter-otlp-proto-http==1.27.0
    # via opentelemetry-exporter-otlp
opentelemetry-instrumentation==0.48b0
    # via uptrace
opentelemetry-proto==1.27.0
    # via opentelemetry-exporter-otlp-proto-common
    # via opentelemetry-exporter-otlp-proto-grpc
    # via opentelemetry-exporter-otlp-proto-http
opentelemetry-sdk==1.27.0
    # via mlflow-skinny
    # via opentelemetry-exporter-otlp-proto-grpc
    # via opentelemetry-exporter-otlp-proto-http
    # via uptrace
opentelemetry-semantic-conventions==0.48b0
    # via opentelemetry-sdk
orjson==3.10.7
    # via langgraph-sdk
    # via langsmith
packaging==23.2
    # via chainlit
    # via faiss-cpu
    # via gunicorn
    # via huggingface-hub
    # via langchain-core
    # via literalai
    # via marshmallow
    # via matplotlib
    # via mlflow-skinny
    # via sagemaker
    # via transformers
pandas==2.2.3
    # via langchain-cohere
    # via mlflow
    # via sagemaker
parameterized==0.9.0
    # via cohere
pathos==0.3.3
    # via sagemaker
pillow==11.0.0
    # via matplotlib
platformdirs==4.3.6
    # via sagemaker
    # via sagemaker-core
pox==0.3.5
    # via pathos
ppft==1.7.6.9
    # via pathos
propcache==0.2.0
    # via yarl
protobuf==4.25.5
    # via googleapis-common-protos
    # via mlflow-skinny
    # via opentelemetry-proto
    # via sagemaker
    # via transformers
psutil==6.1.0
    # via megaparse-sdk
    # via sagemaker
pyarrow==17.0.0
    # via mlflow
pyasn1==0.6.1
    # via pyasn1-modules
    # via rsa
pyasn1-modules==0.4.1
    # via google-auth
pycryptodome==3.21.0
    # via megaparse-sdk
pydantic==2.9.2
    # via anthropic
    # via chainlit
    # via cohere
    # via fastapi
    # via langchain
    # via langchain-anthropic
    # via langchain-cohere
    # via langchain-core
    # via langchain-mistralai
    # via langsmith
    # via literalai
    # via openai
    # via pydantic-settings
    # via quivr-core
    # via sagemaker-core
pydantic-core==2.23.4
    # via cohere
    # via pydantic
pydantic-settings==2.6.1
    # via langchain-community
pygments==2.18.0
    # via rich
pyjwt==2.9.0
    # via chainlit
pyparsing==3.2.0
    # via matplotlib
python-dateutil==2.8.2
    # via botocore
    # via matplotlib
    # via pandas
python-dotenv==1.0.1
    # via chainlit
    # via megaparse-sdk
    # via pydantic-settings
python-engineio==4.10.1
    # via python-socketio
python-multipart==0.0.9
    # via chainlit
python-socketio==5.11.4
    # via chainlit
pytz==2024.2
    # via pandas
pyyaml==6.0.2
    # via huggingface-hub
    # via langchain
    # via langchain-community
    # via langchain-core
    # via mlflow-skinny
    # via sagemaker
    # via sagemaker-core
    # via transformers
quivr-core @ file:///${PROJECT_ROOT}/../../core
rapidfuzz==3.10.1
    # via quivr-core
referencing==0.35.1
    # via jsonschema
    # via jsonschema-specifications
regex==2024.9.11
    # via tiktoken
    # via transformers
requests==2.32.3
    # via cohere
    # via databricks-sdk
    # via docker
    # via huggingface-hub
    # via langchain
    # via langchain-community
    # via langsmith
    # via mlflow-skinny
    # via opentelemetry-exporter-otlp-proto-http
    # via requests-toolbelt
    # via sagemaker
    # via tiktoken
    # via transformers
requests-toolbelt==1.0.0
    # via langsmith
rich==13.9.2
    # via quivr-core
    # via sagemaker-core
rpds-py==0.20.0
    # via jsonschema
    # via referencing
rsa==4.9
    # via google-auth
s3transfer==0.10.3
    # via boto3
safetensors==0.4.5
    # via transformers
sagemaker==2.232.2
    # via cohere
sagemaker-core==1.0.10
    # via sagemaker
sagemaker-mlflow==0.1.0
    # via sagemaker
schema==0.7.7
    # via sagemaker
scikit-learn==1.5.2
    # via mlflow
scipy==1.14.1
    # via mlflow
    # via scikit-learn
sentencepiece==0.2.0
    # via transformers
setuptools==75.2.0
    # via opentelemetry-instrumentation
simple-websocket==1.1.0
    # via python-engineio
six==1.16.0
    # via google-pasta
    # via python-dateutil
smdebug-rulesconfig==1.0.1
    # via sagemaker
smmap==5.0.1
    # via gitdb
sniffio==1.3.1
    # via anthropic
    # via anyio
    # via httpx
    # via openai
sqlalchemy==2.0.36
    # via alembic
    # via langchain
    # via langchain-community
    # via mlflow
sqlparse==0.5.1
    # via mlflow-skinny
starlette==0.41.2
    # via chainlit
    # via fastapi
syncer==2.0.3
    # via chainlit
tabulate==0.9.0
    # via langchain-cohere
tblib==3.0.0
    # via sagemaker
tenacity==8.5.0
    # via langchain
    # via langchain-community
    # via langchain-core
threadpoolctl==3.5.0
    # via scikit-learn
tiktoken==0.8.0
    # via langchain-openai
    # via quivr-core
tokenizers==0.20.1
    # via cohere
    # via langchain-mistralai
    # via transformers
tomli==2.0.2
    # via chainlit
tqdm==4.66.5
    # via huggingface-hub
    # via openai
    # via sagemaker
    # via transformers
transformers==4.45.2
    # via quivr-core
types-pyyaml==6.0.12.20240917
    # via quivr-core
types-requests==2.32.0.20241016
    # via cohere
typing-extensions==4.12.2
    # via alembic
    # via anthropic
    # via cohere
    # via fastapi
    # via huggingface-hub
    # via langchain-core
    # via openai
    # via opentelemetry-sdk
    # via pydantic
    # via pydantic-core
    # via sqlalchemy
    # via typing-inspect
typing-inspect==0.9.0
    # via dataclasses-json
tzdata==2024.2
    # via pandas
uptrace==1.27.0
    # via chainlit
urllib3==2.2.3
    # via botocore
    # via docker
    # via requests
    # via sagemaker
    # via types-requests
uvicorn==0.25.0
    # via chainlit
watchfiles==0.20.0
    # via chainlit
werkzeug==3.0.4
    # via flask
wrapt==1.16.0
    # via deprecated
    # via opentelemetry-instrumentation
wsproto==1.2.0
    # via simple-websocket
yarl==1.15.4
    # via aiohttp
zipp==3.20.2
    # via importlib-metadata
