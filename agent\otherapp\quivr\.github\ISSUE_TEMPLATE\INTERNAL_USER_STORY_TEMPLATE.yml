name: QUIVR INTERNAL ONLY - User story
description: Use this form for user stories.
title: "User story:"
labels: ["user story"]
body:
  - type: markdown
    attributes:
      value: |
        **Epic**

        Include the issue that represents the epic.

  - type: input
    id: epic-link
    attributes:
      label: Link to the Epic
      placeholder: Paste the link to the related epic here...
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        **Functional**

        Detail the functionality and provide context and motivation.

  - type: textarea
    id: functionality-detail
    attributes:
      label: Explain the Functionality
      placeholder: Detail the user story functionality here...
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        **Schema**

  - type: markdown
    attributes:
      value: |
        ### Tech

  - type: markdown
    attributes:
      value: |
        ### Tech To-dos

  - type: textarea
    id: tech-todos
    attributes:
      label: Tech To-dos
      placeholder: Detail the tech to-dos here...
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        ### Tests

  - type: textarea
    id: tests
    attributes:
      label: Tests
      placeholder: Detail the tests here...
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        ### Validation Checks

  - type: textarea
    id: validation-checks
    attributes:
      label: Validation Checks
      placeholder: Detail the validation checks here...
    validations:
      required: true
