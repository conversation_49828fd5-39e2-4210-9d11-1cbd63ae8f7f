# Using a slim version for a smaller base image
FROM python:3.11.6-slim-bullseye

# Install GEOS library, Rust, and other dependencies, then clean up
RUN apt-get clean && apt-get update && apt-get install -y \
    curl \
    gcc \
    autoconf \
    automake \
    build-essential \
    # Additional dependencies for document handling
    libmagic-dev \
    tesseract-ocr \
    poppler-utils \
    libreoffice \
    pandoc && \
    rm -rf /var/lib/apt/lists/*

# Set the working directory
WORKDIR /code

# Install Poetry
RUN curl -sSL https://install.python-poetry.org | POETRY_HOME=/opt/poetry python && \
    cd /usr/local/bin && \
    ln -s /opt/poetry/bin/poetry && \
    poetry config virtualenvs.create false

# Add Poetry to PATH
ENV PATH="/root/.local/bin:$PATH"

# Copy the current directory contents into the container at /app
COPY ./pyproject.toml ./poetry.lock* /code/

RUN python3 -m pip install nltk && python3 -c "import nltk; nltk.download('punkt')" \
    && python3 -c "import nltk; nltk.download('averaged_perceptron_tagger')"

# Install project dependencies
RUN poetry install  --with test

ENV PYTHONPATH=/code
