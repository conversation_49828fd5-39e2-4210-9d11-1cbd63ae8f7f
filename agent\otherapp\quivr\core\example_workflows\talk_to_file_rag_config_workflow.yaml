{
  "max_files": 20,
  "llm_config": { "temperature": 0.3, "max_context_tokens": 20000 },
  "max_history": 10,
  "reranker_config":
    { "model": "rerank-v3.5", "top_n": 10, "supplier": "cohere" },
  "workflow_config":
    {
      "name": "Standard RAG",
      "nodes":
        [
          {
            "name": "START",
            "edges": ["filter_history"],
            "description": "Starting workflow",
          },
          {
            "name": "filter_history",
            "edges": ["retrieve"],
            "description": "Filtering history",
          },
          {
            "name": "retrieve",
            "edges": ["retrieve_full_documents_context"],
            "description": "Retrieving relevant information",
          },
          {
            "name": "retrieve_full_documents_context",
            "edges": ["generate_zendesk_rag"],
            "description": "Retrieving full tickets context",
          },
          {
            "name": "generate_zendesk_rag",
            "edges": ["END"],
            "description": "Generating answer",
          },
        ],
    },
}
