[project]
# Whether to enable telemetry (default: true). No personal data is collected.
enable_telemetry = true


# List of environment variables to be provided by each user to use the app.
user_env = []

# Duration (in seconds) during which the session is saved when the connection is lost
session_timeout = 3600

# Enable third parties caching (e.g <PERSON><PERSON>hain cache)
cache = false

# Authorized origins
allow_origins = ["*"]

# Follow symlink for asset mount (see https://github.com/Chainlit/chainlit/issues/317)
# follow_symlink = false

[features]
# Process and display HTML in messages. This can be a security risk (see https://stackoverflow.com/questions/19603097/why-is-it-dangerous-to-render-user-generated-html-or-javascript)
unsafe_allow_html = false

# Process and display mathematical expressions. This can clash with "$" characters in messages.
latex = false

# Automatically tag threads with the current chat profile (if a chat profile is used)
auto_tag_thread = true

# Authorize users to spontaneously upload files with messages
[features.spontaneous_file_upload]
    enabled = false
    accept = ["*/*"]
    max_files = 20
    max_size_mb = 500

[features.audio]
    # Threshold for audio recording
    min_decibels = -45
    # Delay for the user to start speaking in MS
    initial_silence_timeout = 3000
    # Delay for the user to continue speaking in MS. If the user stops speaking for this duration, the recording will stop.
    silence_timeout = 1500
    # Above this duration (MS), the recording will forcefully stop.
    max_duration = 15000
    # Duration of the audio chunks in MS
    chunk_duration = 1000
    # Sample rate of the audio
    sample_rate = 44100

edit_message = true

[UI]
# Name of the assistant.
name = "Quivr"

# Description of the assistant. This is used for HTML tags.
description = "Demo of Quivr"

# Large size content are by default collapsed for a cleaner ui
default_collapse_content = true

# Chain of Thought (CoT) display mode. Can be "hidden", "tool_call" or "full".
cot = "hidden"

# Link to your github repo. This will add a github button in the UI's header.
github = "https://github.com/quivrhq/quivr"

# Specify a CSS file that can be used to customize the user interface.
# The CSS file can be served from the public directory or via an external link.
# custom_css = "/public/style.css"

# Specify a Javascript file that can be used to customize the user interface.
# The Javascript file can be served from the public directory.
# custom_js = "/public/test.js"

# Specify a custom font url.
# custom_font = "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap"

# Specify a custom meta image url.
# custom_meta_image_url = "https://chainlit-cloud.s3.eu-west-3.amazonaws.com/logo/chainlit_banner.png"

# Specify a custom build directory for the frontend.
# This can be used to customize the frontend code.
# Be careful: If this is a relative path, it should not start with a slash.
# custom_build = "./public/build"

[UI.theme]
    default = "dark"
    font_family = "Tahoma,Verdana,Segoe,sans-serif"

# Override default MUI light theme. (Check theme.ts)
[UI.theme.light]
    background = "#fcfcfc"
    paper = "#f8f8f8"

    [UI.theme.light.primary]
        main = "#6142d4"
        dark = "#6e53cf"
        light = "#6e53cf30"
    [UI.theme.light.text]
        primary = "#1f1f1f"
        secondary = "#818080"

# Override default MUI dark theme. (Check theme.ts)
[UI.theme.dark]
    background = "#252525"
    paper = "#1f1f1f"

    [UI.theme.dark.primary]
        main = "#6142d4"
        dark = "#6e53cf"
        light = "#6e53cf30"
    [UI.theme.dark.text]
        primary = "#f4f4f4"
        secondary = "#c8c8c8"

[meta]
generated_by = "1.1.402"
