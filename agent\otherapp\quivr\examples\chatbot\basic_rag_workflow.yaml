workflow_config:
  name: "standard RAG"
  nodes:
    - name: "START"
      edges: ["filter_history"]

    - name: "filter_history"
      edges: ["rewrite"]

    - name: "rewrite"
      edges: ["retrieve"]

    - name: "retrieve"
      edges: ["generate_rag"]

    - name: "generate_rag" # the name of the last node, from which we want to stream the answer to the user
      edges: ["END"]
      tools:
        - name: "cited_answer"

# Maximum number of previous conversation iterations
# to include in the context of the answer
max_history: 10

# Reranker configuration
# reranker_config:
#   # The reranker supplier to use
#   supplier: "cohere"

#   # The model to use for the reranker for the given supplier
#   model: "rerank-multilingual-v3.0"

#   # Number of chunks returned by the reranker
#   top_n: 5

# Configuration for the LLM
llm_config:

  # maximum number of tokens passed to the LLM to generate the answer
  max_output_tokens: 4000

  # temperature for the LLM
  temperature: 0.7
