* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}


body {
    color: #f4f4f4;
    background-color: #252525;
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    min-height: 100vh;
}

.primary {
    background-color: #6142d4;
}

button {
    background-color: #6142d4;
    border: none;
    padding: .75rem 2rem;
    border-radius: 0.5rem;
    color: #f4f4f4;
    cursor: pointer;
}

canvas {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: #252525;
    z-index: -1;
}

.record-btn {
    background-color: #f5f5f5;
    border: none;
    outline: none;
    width: 256px;
    height: 256px;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 50%;
    background-size: 50%;
    transition: background-color 200ms ease-in, transform 200ms ease-out;
}

.record-btn:hover {
    background-color: #fff;
    transform: scale(1.025);
}

.record-btn:active {
    background-color: #e2e2e2;
    transform: scale(0.975);
}

.record-btn[data-recording="true"] {
    background-image: url("./mic.svg");
}

.record-btn[data-recording="false"] {
    background-image: url("./mic-off.svg");
}

.record-btn[data-pending="true"] {
    background-image: url("./loader.svg") !important;
    animation: spin 1s linear infinite;
}

.hidden {
    display: none !important;
    visibility: hidden;
}

.custom-file-input {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.custom-file-input input[type="file"] {
  display: none;
}

.custom-file-input label {
  border: solid 2px #6142d4;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: background-color 0.3s;
}

.custom-file-input label:hover {
  background-color: #6142d4;
}

.custom-file-input span {
  font-size: 14px;
  color: #f4f4f4;
}

/* Adjust appearance when a file is selected */
.custom-file-input span.file-selected {
  color: #ffffff;
  font-weight: bold;
}

/* 
# Override default MUI light theme. (Check theme.ts)
[UI.theme.light]
    background = "#fcfcfc"
    paper = "#f8f8f8"

    [UI.theme.light.primary]
        main = "#6142d4"
        dark = "#6e53cf"
        light = "#6e53cf30"
    [UI.theme.light.text]
        primary = "#1f1f1f"
        secondary = "#818080"

# Override default MUI dark theme. (Check theme.ts)
[UI.theme.dark]
    background = "#252525"
    paper = "#1f1f1f"

    [UI.theme.dark.primary]
        main = "#6142d4"
        dark = "#6e53cf"
        light = "#6e53cf30"
    [UI.theme.dark.text]
        primary = "#f4f4f4"
        secondary = "#c8c8c8"

*/

.loader {
    border: 4px solid #f3f3f3;
    border-radius: 50%;
    border-top: 4px solid #3498db;
    width: 50px;
    height: 50px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    position: absolute;
    /* Center the loader in the viewport */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    /* Hide it by default */
}

@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}