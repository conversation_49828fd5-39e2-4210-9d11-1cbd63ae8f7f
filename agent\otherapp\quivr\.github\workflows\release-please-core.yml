name: release-please-core

on:
  push:
    branches:
      - main
  workflow_dispatch:

permissions:
  contents: write
  pull-requests: write

jobs:
  release-please:
    runs-on: ubuntu-latest
    outputs:
      release_created: ${{ steps.release.outputs['core--release_created'] }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Fetch all history for tags and releases

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Run release-please
        id: release
        uses: google-github-actions/release-please-action@v4
        with:
          path: core
          token: ${{ secrets.RELEASE_PLEASE_TOKEN }}
       

  deploy:
    if: needs.release-please.outputs.release_created == 'true'
    needs: release-please
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: core
    steps:
      - uses: actions/checkout@v4
      - name: Install Rye
        uses: eifinger/setup-rye@v2
        with:
          enable-cache: true
      - name: Rye Sync
        run: UV_INDEX_STRATEGY=unsafe-first-match rye sync --no-lock
      - name: Rye Build
        run: rye build
      - name: Rye Publish
        run: rye publish --token ${{ secrets.PYPI_API_TOKEN }} --yes --skip-existing