name: Run Tests with Tika Server

on:
  push:
    paths:
      - "core/**"
  pull_request:
    paths:
      - "core/**"
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      tika:
        image: apache/tika
        ports:
          - 9998:9998

    steps:
      - name: 👀 Checkout code
        uses: actions/checkout@v2

      - name: 🔨 Install the latest version of rye
        uses: eifinger/setup-rye@v4
        with:
          enable-cache: true
          working-directory: backend
      - name: 🔄 Sync dependencies
        run: |
          cd core
          UV_INDEX_STRATEGY=unsafe-first-match rye sync --no-lock

      - name: Run tests
        env:
          TIKA_URL: http://localhost:9998/tika
          OPENAI_API_KEY: this-is-a-test-key
        run: |
          sudo apt-get update
          sudo apt-get install -y libmagic-dev poppler-utils libreoffice tesseract-ocr  pandoc
          cd core
          rye test -p quivr-core
