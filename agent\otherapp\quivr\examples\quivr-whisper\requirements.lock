# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

aiofiles==24.1.0
    # via quivr-core
aiohappyeyeballs==2.4.3
    # via aiohttp
aiohttp==3.11.6
    # via langchain
    # via langchain-community
aiosignal==1.3.1
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anthropic==0.39.0
    # via langchain-anthropic
anyio==4.6.2.post1
    # via anthropic
    # via httpx
    # via openai
asgiref==3.8.1
    # via flask
attrs==24.2.0
    # via aiohttp
blinker==1.9.0
    # via flask
cachelib==0.9.0
    # via flask-caching
certifi==2024.8.30
    # via httpcore
    # via httpx
    # via requests
charset-normalizer==3.4.0
    # via requests
click==8.1.7
    # via flask
cohere==5.11.4
    # via langchain-cohere
dataclasses-json==0.6.7
    # via langchain-community
defusedxml==0.7.1
    # via langchain-anthropic
distro==1.9.0
    # via anthropic
    # via openai
faiss-cpu==1.9.0.post1
    # via quivr-core
fastavro==1.9.7
    # via cohere
filelock==3.16.1
    # via huggingface-hub
    # via transformers
flask==3.1.0
    # via flask-caching
flask-caching==2.3.0
frozenlist==1.5.0
    # via aiohttp
    # via aiosignal
fsspec==2024.10.0
    # via huggingface-hub
h11==0.14.0
    # via httpcore
httpcore==1.0.7
    # via httpx
httpx==0.27.2
    # via anthropic
    # via cohere
    # via langchain-mistralai
    # via langgraph-sdk
    # via langsmith
    # via megaparse-sdk
    # via openai
    # via quivr-core
httpx-sse==0.4.0
    # via cohere
    # via langchain-community
    # via langchain-mistralai
huggingface-hub==0.26.2
    # via tokenizers
    # via transformers
idna==3.10
    # via anyio
    # via httpx
    # via requests
    # via yarl
itsdangerous==2.2.0
    # via flask
jinja2==3.1.4
    # via flask
jiter==0.7.1
    # via anthropic
    # via openai
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
langchain==0.3.9
    # via langchain-community
    # via quivr-core
langchain-anthropic==0.3.0
    # via quivr-core
langchain-cohere==0.3.3
    # via quivr-core
langchain-community==0.3.9
    # via langchain-experimental
    # via quivr-core
langchain-core==0.3.21
    # via langchain
    # via langchain-anthropic
    # via langchain-cohere
    # via langchain-community
    # via langchain-experimental
    # via langchain-mistralai
    # via langchain-openai
    # via langchain-text-splitters
    # via langgraph
    # via langgraph-checkpoint
    # via quivr-core
langchain-experimental==0.3.3
    # via langchain-cohere
langchain-mistralai==0.2.3
    # via quivr-core
langchain-openai==0.2.11
    # via quivr-core
langchain-text-splitters==0.3.2
    # via langchain
langgraph==0.2.56
    # via quivr-core
langgraph-checkpoint==2.0.9
    # via langgraph
langgraph-sdk==0.1.46
    # via langgraph
langsmith==0.1.143
    # via langchain
    # via langchain-community
    # via langchain-core
loguru==0.7.2
    # via megaparse-sdk
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
    # via quivr-core
    # via werkzeug
marshmallow==3.23.1
    # via dataclasses-json
mdurl==0.1.2
    # via markdown-it-py
megaparse-sdk==0.1.10
    # via quivr-core
msgpack==1.1.0
    # via langgraph-checkpoint
multidict==6.1.0
    # via aiohttp
    # via yarl
mypy-extensions==1.0.0
    # via typing-inspect
nats-py==2.9.0
    # via megaparse-sdk
numpy==1.26.4
    # via faiss-cpu
    # via langchain
    # via langchain-community
    # via pandas
    # via transformers
openai==1.54.5
    # via langchain-openai
orjson==3.10.11
    # via langgraph-sdk
    # via langsmith
packaging==24.2
    # via faiss-cpu
    # via huggingface-hub
    # via langchain-core
    # via marshmallow
    # via transformers
pandas==2.2.3
    # via langchain-cohere
parameterized==0.9.0
    # via cohere
propcache==0.2.0
    # via aiohttp
    # via yarl
protobuf==5.28.3
    # via transformers
psutil==6.1.0
    # via megaparse-sdk
pycryptodome==3.21.0
    # via megaparse-sdk
pydantic==2.9.2
    # via anthropic
    # via cohere
    # via langchain
    # via langchain-anthropic
    # via langchain-cohere
    # via langchain-core
    # via langchain-mistralai
    # via langsmith
    # via openai
    # via pydantic-settings
    # via quivr-core
pydantic-core==2.23.4
    # via cohere
    # via pydantic
pydantic-settings==2.6.1
    # via langchain-community
pygments==2.18.0
    # via rich
python-dateutil==2.8.2
    # via pandas
python-dotenv==1.0.1
    # via megaparse-sdk
    # via pydantic-settings
pytz==2024.2
    # via pandas
pyyaml==6.0.2
    # via huggingface-hub
    # via langchain
    # via langchain-community
    # via langchain-core
    # via transformers
quivr-core @ file:///${PROJECT_ROOT}/../../core
rapidfuzz==3.10.1
    # via quivr-core
regex==2024.11.6
    # via tiktoken
    # via transformers
requests==2.32.3
    # via cohere
    # via huggingface-hub
    # via langchain
    # via langchain-community
    # via langsmith
    # via requests-toolbelt
    # via tiktoken
    # via transformers
requests-toolbelt==1.0.0
    # via langsmith
rich==13.9.4
    # via quivr-core
safetensors==0.4.5
    # via transformers
sentencepiece==0.2.0
    # via transformers
six==1.16.0
    # via python-dateutil
sniffio==1.3.1
    # via anthropic
    # via anyio
    # via httpx
    # via openai
sqlalchemy==2.0.36
    # via langchain
    # via langchain-community
tabulate==0.9.0
    # via langchain-cohere
tenacity==8.5.0
    # via langchain
    # via langchain-community
    # via langchain-core
tiktoken==0.8.0
    # via langchain-openai
    # via quivr-core
tokenizers==0.20.3
    # via cohere
    # via langchain-mistralai
    # via transformers
tqdm==4.67.0
    # via huggingface-hub
    # via openai
    # via transformers
transformers==4.46.3
    # via quivr-core
types-pyyaml==6.0.12.20240917
    # via quivr-core
types-requests==2.32.0.20241016
    # via cohere
typing-extensions==4.12.2
    # via anthropic
    # via cohere
    # via huggingface-hub
    # via langchain-core
    # via openai
    # via pydantic
    # via pydantic-core
    # via sqlalchemy
    # via typing-inspect
typing-inspect==0.9.0
    # via dataclasses-json
tzdata==2024.2
    # via pandas
urllib3==2.2.3
    # via requests
    # via types-requests
werkzeug==3.1.3
    # via flask
yarl==1.17.2
    # via aiohttp
