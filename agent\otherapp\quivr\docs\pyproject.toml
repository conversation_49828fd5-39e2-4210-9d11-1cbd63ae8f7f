[project]
name = "docs"
version = "0.1.0"
description = "Add your description here"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
dependencies = [
    "quivr-core @ file:///${PROJECT_ROOT}/../core",
    "mkdocs>=1.6.1",
    "mkdocstrings[python]>=0.26.0",
    "mkdocs-jupyter>=0.24.8",
    "mkdocs-include-dir-to-nav>=1.2.0",
    "mkdocs-material>=9.5.34",
    "mkdocs-redirects>=1.2.1",
]
readme = "README.md"
requires-python = ">= 3.8"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.rye]
managed = true
dev-dependencies = []
virtual = true

[tool.rye.scripts]
docs = "mkdocs serve"
build_docs = "mkdocs build --strict"

[tool.basedpyright]
include = ["src/"]
# Ensure that it uses the .venv that we created for this project with the lockfile
venvPath="./"
venv=".venv"
# We really only care about some import issues, so we disable everything and report on missing imports:
typeCheckingMode = "off"
reportMissingImports = true


[tool.hatch.metadata]
allow-direct-references = true
