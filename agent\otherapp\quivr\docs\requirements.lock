# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

aiofiles==24.1.0
    # via quivr-core
aiohappyeyeballs==2.4.3
    # via aiohttp
aiohttp==3.10.10
    # via langchain
    # via langchain-community
aiosignal==1.3.1
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anthropic==0.40.0
    # via langchain-anthropic
anyio==4.6.2.post1
    # via anthropic
    # via httpx
    # via openai
appnope==0.1.4
    # via ipykernel
asttokens==2.4.1
    # via stack-data
attrs==24.2.0
    # via aiohttp
    # via jsonschema
    # via referencing
babel==2.16.0
    # via mkdocs-material
beautifulsoup4==4.12.3
    # via nbconvert
bleach==6.1.0
    # via nbconvert
certifi==2024.8.30
    # via httpcore
    # via httpx
    # via requests
charset-normalizer==3.3.2
    # via requests
click==8.1.7
    # via mkdocs
    # via mkdocstrings
cohere==5.13.2
    # via langchain-cohere
colorama==0.4.6
    # via griffe
    # via mkdocs-material
comm==0.2.2
    # via ipykernel
dataclasses-json==0.6.7
    # via langchain-community
debugpy==1.8.5
    # via ipykernel
decorator==5.1.1
    # via ipython
defusedxml==0.7.1
    # via langchain-anthropic
    # via nbconvert
distro==1.9.0
    # via anthropic
    # via openai
executing==2.1.0
    # via stack-data
faiss-cpu==1.9.0.post1
    # via quivr-core
fastavro==1.9.7
    # via cohere
fastjsonschema==2.20.0
    # via nbformat
filelock==3.16.1
    # via huggingface-hub
    # via transformers
frozenlist==1.4.1
    # via aiohttp
    # via aiosignal
fsspec==2024.9.0
    # via huggingface-hub
ghp-import==2.1.0
    # via mkdocs
griffe==1.2.0
    # via mkdocstrings-python
h11==0.14.0
    # via httpcore
httpcore==1.0.6
    # via httpx
httpx==0.27.2
    # via anthropic
    # via cohere
    # via langchain-mistralai
    # via langgraph-sdk
    # via langsmith
    # via megaparse-sdk
    # via openai
    # via quivr-core
httpx-sse==0.4.0
    # via cohere
    # via langchain-community
    # via langchain-mistralai
    # via langgraph-sdk
huggingface-hub==0.25.2
    # via tokenizers
    # via transformers
idna==3.8
    # via anyio
    # via httpx
    # via requests
    # via yarl
ipykernel==6.29.5
    # via mkdocs-jupyter
ipython==8.27.0
    # via ipykernel
jedi==0.19.1
    # via ipython
jinja2==3.1.4
    # via mkdocs
    # via mkdocs-material
    # via mkdocstrings
    # via nbconvert
jiter==0.6.1
    # via anthropic
    # via openai
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.23.0
    # via nbformat
jsonschema-specifications==2023.12.1
    # via jsonschema
jupyter-client==8.6.2
    # via ipykernel
    # via nbclient
jupyter-core==5.7.2
    # via ipykernel
    # via jupyter-client
    # via nbclient
    # via nbconvert
    # via nbformat
jupyterlab-pygments==0.3.0
    # via nbconvert
jupytext==1.16.4
    # via mkdocs-jupyter
langchain==0.3.9
    # via langchain-community
    # via quivr-core
langchain-anthropic==0.3.0
    # via quivr-core
langchain-cohere==0.3.3
    # via quivr-core
langchain-community==0.3.9
    # via langchain-experimental
    # via quivr-core
langchain-core==0.3.21
    # via langchain
    # via langchain-anthropic
    # via langchain-cohere
    # via langchain-community
    # via langchain-experimental
    # via langchain-mistralai
    # via langchain-openai
    # via langchain-text-splitters
    # via langgraph
    # via langgraph-checkpoint
    # via quivr-core
langchain-experimental==0.3.3
    # via langchain-cohere
langchain-mistralai==0.2.3
    # via quivr-core
langchain-openai==0.2.11
    # via quivr-core
langchain-text-splitters==0.3.2
    # via langchain
langgraph==0.2.38
    # via quivr-core
langgraph-checkpoint==2.0.1
    # via langgraph
langgraph-sdk==0.1.33
    # via langgraph
langsmith==0.1.135
    # via langchain
    # via langchain-community
    # via langchain-core
loguru==0.7.2
    # via megaparse-sdk
markdown==3.7
    # via mkdocs
    # via mkdocs-autorefs
    # via mkdocs-material
    # via mkdocstrings
    # via pymdown-extensions
markdown-it-py==3.0.0
    # via jupytext
    # via mdit-py-plugins
    # via rich
markupsafe==2.1.5
    # via jinja2
    # via mkdocs
    # via mkdocs-autorefs
    # via mkdocstrings
    # via nbconvert
    # via quivr-core
marshmallow==3.22.0
    # via dataclasses-json
matplotlib-inline==0.1.7
    # via ipykernel
    # via ipython
mdit-py-plugins==0.4.1
    # via jupytext
mdurl==0.1.2
    # via markdown-it-py
megaparse-sdk==0.1.10
    # via quivr-core
mergedeep==1.3.4
    # via mkdocs
    # via mkdocs-get-deps
mistune==3.0.2
    # via nbconvert
mkdocs==1.6.1
    # via mkdocs-autorefs
    # via mkdocs-include-dir-to-nav
    # via mkdocs-jupyter
    # via mkdocs-material
    # via mkdocs-redirects
    # via mkdocstrings
mkdocs-autorefs==1.2.0
    # via mkdocstrings
    # via mkdocstrings-python
mkdocs-get-deps==0.2.0
    # via mkdocs
mkdocs-include-dir-to-nav==1.2.0
mkdocs-jupyter==0.24.8
mkdocs-material==9.5.34
    # via mkdocs-jupyter
mkdocs-material-extensions==1.3.1
    # via mkdocs-material
mkdocs-redirects==1.2.1
mkdocstrings==0.26.0
    # via mkdocstrings-python
mkdocstrings-python==1.10.9
    # via mkdocstrings
msgpack==1.1.0
    # via langgraph-checkpoint
multidict==6.1.0
    # via aiohttp
    # via yarl
mypy-extensions==1.0.0
    # via typing-inspect
nats-py==2.9.0
    # via megaparse-sdk
nbclient==0.10.0
    # via nbconvert
nbconvert==7.16.4
    # via mkdocs-jupyter
nbformat==5.10.4
    # via jupytext
    # via nbclient
    # via nbconvert
nest-asyncio==1.6.0
    # via ipykernel
numpy==1.26.4
    # via faiss-cpu
    # via langchain
    # via langchain-community
    # via pandas
    # via transformers
openai==1.56.2
    # via langchain-openai
orjson==3.10.7
    # via langgraph-sdk
    # via langsmith
packaging==24.1
    # via faiss-cpu
    # via huggingface-hub
    # via ipykernel
    # via jupytext
    # via langchain-core
    # via marshmallow
    # via mkdocs
    # via nbconvert
    # via transformers
paginate==0.5.7
    # via mkdocs-material
pandas==2.2.3
    # via langchain-cohere
pandocfilters==1.5.1
    # via nbconvert
parameterized==0.9.0
    # via cohere
parso==0.8.4
    # via jedi
pathspec==0.12.1
    # via mkdocs
pexpect==4.9.0
    # via ipython
platformdirs==4.2.2
    # via jupyter-core
    # via mkdocs-get-deps
    # via mkdocstrings
prompt-toolkit==3.0.47
    # via ipython
propcache==0.2.0
    # via yarl
protobuf==5.28.2
    # via transformers
psutil==6.1.0
    # via ipykernel
    # via megaparse-sdk
ptyprocess==0.7.0
    # via pexpect
pure-eval==0.2.3
    # via stack-data
pycryptodome==3.21.0
    # via megaparse-sdk
pydantic==2.9.2
    # via anthropic
    # via cohere
    # via langchain
    # via langchain-anthropic
    # via langchain-cohere
    # via langchain-core
    # via langchain-mistralai
    # via langsmith
    # via openai
    # via pydantic-settings
    # via quivr-core
pydantic-core==2.23.4
    # via cohere
    # via pydantic
pydantic-settings==2.6.1
    # via langchain-community
pygments==2.18.0
    # via ipython
    # via mkdocs-jupyter
    # via mkdocs-material
    # via nbconvert
    # via rich
pymdown-extensions==10.9
    # via mkdocs-material
    # via mkdocstrings
python-dateutil==2.9.0.post0
    # via ghp-import
    # via jupyter-client
    # via pandas
python-dotenv==1.0.1
    # via megaparse-sdk
    # via pydantic-settings
pytz==2024.2
    # via pandas
pyyaml==6.0.2
    # via huggingface-hub
    # via jupytext
    # via langchain
    # via langchain-community
    # via langchain-core
    # via mkdocs
    # via mkdocs-get-deps
    # via pymdown-extensions
    # via pyyaml-env-tag
    # via transformers
pyyaml-env-tag==0.1
    # via mkdocs
pyzmq==26.2.0
    # via ipykernel
    # via jupyter-client
quivr-core @ file:///${PROJECT_ROOT}/../core
rapidfuzz==3.10.1
    # via quivr-core
referencing==0.35.1
    # via jsonschema
    # via jsonschema-specifications
regex==2024.7.24
    # via mkdocs-material
    # via tiktoken
    # via transformers
requests==2.32.3
    # via cohere
    # via huggingface-hub
    # via langchain
    # via langchain-community
    # via langsmith
    # via mkdocs-material
    # via requests-toolbelt
    # via tiktoken
    # via transformers
requests-toolbelt==1.0.0
    # via langsmith
rich==13.9.2
    # via quivr-core
rpds-py==0.20.0
    # via jsonschema
    # via referencing
safetensors==0.4.5
    # via transformers
sentencepiece==0.2.0
    # via transformers
six==1.16.0
    # via asttokens
    # via bleach
    # via python-dateutil
sniffio==1.3.1
    # via anthropic
    # via anyio
    # via httpx
    # via openai
soupsieve==2.6
    # via beautifulsoup4
sqlalchemy==2.0.36
    # via langchain
    # via langchain-community
stack-data==0.6.3
    # via ipython
tabulate==0.9.0
    # via langchain-cohere
tenacity==8.5.0
    # via langchain
    # via langchain-community
    # via langchain-core
tiktoken==0.8.0
    # via langchain-openai
    # via quivr-core
tinycss2==1.3.0
    # via nbconvert
tokenizers==0.20.1
    # via cohere
    # via langchain-mistralai
    # via transformers
tornado==6.4.1
    # via ipykernel
    # via jupyter-client
tqdm==4.66.5
    # via huggingface-hub
    # via openai
    # via transformers
traitlets==5.14.3
    # via comm
    # via ipykernel
    # via ipython
    # via jupyter-client
    # via jupyter-core
    # via matplotlib-inline
    # via nbclient
    # via nbconvert
    # via nbformat
transformers==4.45.2
    # via quivr-core
types-pyyaml==6.0.12.20240917
    # via quivr-core
types-requests==2.32.0.20241016
    # via cohere
typing-extensions==4.12.2
    # via anthropic
    # via cohere
    # via huggingface-hub
    # via ipython
    # via langchain-core
    # via openai
    # via pydantic
    # via pydantic-core
    # via sqlalchemy
    # via typing-inspect
typing-inspect==0.9.0
    # via dataclasses-json
tzdata==2024.2
    # via pandas
urllib3==2.2.2
    # via requests
    # via types-requests
watchdog==5.0.0
    # via mkdocs
wcwidth==0.2.13
    # via prompt-toolkit
webencodings==0.5.1
    # via bleach
    # via tinycss2
yarl==1.15.4
    # via aiohttp
