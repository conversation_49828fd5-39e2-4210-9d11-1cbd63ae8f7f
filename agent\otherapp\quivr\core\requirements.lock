# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: false
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
aiofiles==24.1.0
    # via quivr-core
aiohappyeyeballs==2.4.3
    # via aiohttp
aiohttp==3.10.10
    # via langchain
    # via langchain-community
aiosignal==1.3.1
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anthropic==0.40.0
    # via langchain-anthropic
anyio==4.6.2.post1
    # via anthropic
    # via httpx
    # via langfuse
    # via openai
attrs==24.2.0
    # via aiohttp
backoff==2.2.1
    # via langfuse
certifi==2024.8.30
    # via httpcore
    # via httpx
    # via requests
charset-normalizer==3.4.0
    # via requests
cohere==5.11.1
    # via langchain-cohere
dataclasses-json==0.6.7
    # via langchain-community
defusedxml==0.7.1
    # via langchain-anthropic
distro==1.9.0
    # via anthropic
    # via openai
faiss-cpu==1.9.0
    # via quivr-core
fastavro==1.9.7
    # via cohere
fasttext==0.9.3
    # via fasttext-langdetect
fasttext-langdetect==1.0.5
    # via quivr-core
filelock==3.16.1
    # via huggingface-hub
    # via transformers
frozenlist==1.4.1
    # via aiohttp
    # via aiosignal
fsspec==2024.9.0
    # via huggingface-hub
greenlet==3.1.1
    # via sqlalchemy
h11==0.14.0
    # via httpcore
httpcore==1.0.6
    # via httpx
httpx==0.27.2
    # via anthropic
    # via cohere
    # via langchain-mistralai
    # via langfuse
    # via langgraph-sdk
    # via langsmith
    # via megaparse-sdk
    # via openai
    # via quivr-core
httpx-sse==0.4.0
    # via cohere
    # via langchain-community
    # via langchain-mistralai
    # via langgraph-sdk
huggingface-hub==0.25.2
    # via tokenizers
    # via transformers
idna==3.10
    # via anyio
    # via httpx
    # via langfuse
    # via requests
    # via yarl
jiter==0.6.1
    # via anthropic
    # via openai
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
langchain==0.3.9
    # via langchain-community
    # via quivr-core
langchain-anthropic==0.3.0
    # via quivr-core
langchain-cohere==0.3.3
    # via quivr-core
langchain-community==0.3.9
    # via langchain-experimental
    # via quivr-core
langchain-core==0.3.33
    # via langchain
    # via langchain-anthropic
    # via langchain-cohere
    # via langchain-community
    # via langchain-experimental
    # via langchain-mistralai
    # via langchain-openai
    # via langchain-text-splitters
    # via langgraph
    # via langgraph-checkpoint
    # via quivr-core
langchain-experimental==0.3.3
    # via langchain-cohere
langchain-mistralai==0.2.3
    # via quivr-core
langchain-openai==0.3.3
    # via quivr-core
langchain-text-splitters==0.3.2
    # via langchain
langfuse==2.57.0
    # via quivr-core
langgraph==0.2.38
    # via quivr-core
langgraph-checkpoint==2.0.1
    # via langgraph
langgraph-sdk==0.1.33
    # via langgraph
langsmith==0.1.135
    # via langchain
    # via langchain-community
    # via langchain-core
loguru==0.7.2
    # via megaparse-sdk
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via quivr-core
marshmallow==3.22.0
    # via dataclasses-json
mdurl==0.1.2
    # via markdown-it-py
megaparse-sdk==0.1.10
    # via quivr-core
msgpack==1.1.0
    # via langgraph-checkpoint
multidict==6.1.0
    # via aiohttp
    # via yarl
mypy-extensions==1.0.0
    # via typing-inspect
nats-py==2.9.0
    # via megaparse-sdk
numpy==1.26.4
    # via faiss-cpu
    # via fasttext
    # via langchain
    # via langchain-community
    # via pandas
    # via transformers
openai==1.61.0
    # via langchain-openai
orjson==3.10.7
    # via langgraph-sdk
    # via langsmith
packaging==24.1
    # via faiss-cpu
    # via huggingface-hub
    # via langchain-core
    # via langfuse
    # via marshmallow
    # via transformers
pandas==2.2.3
    # via langchain-cohere
parameterized==0.9.0
    # via cohere
propcache==0.2.0
    # via yarl
protobuf==5.28.2
    # via transformers
psutil==6.1.0
    # via megaparse-sdk
pybind11==2.13.6
    # via fasttext
pycryptodome==3.21.0
    # via megaparse-sdk
pydantic==2.9.2
    # via anthropic
    # via cohere
    # via langchain
    # via langchain-anthropic
    # via langchain-cohere
    # via langchain-core
    # via langchain-mistralai
    # via langfuse
    # via langsmith
    # via openai
    # via pydantic-settings
    # via quivr-core
pydantic-core==2.23.4
    # via cohere
    # via pydantic
pydantic-settings==2.6.1
    # via langchain-community
pygments==2.18.0
    # via rich
python-dateutil==2.8.2
    # via pandas
python-dotenv==1.0.1
    # via megaparse-sdk
    # via pydantic-settings
pytz==2024.2
    # via pandas
pyyaml==6.0.2
    # via huggingface-hub
    # via langchain
    # via langchain-community
    # via langchain-core
    # via transformers
rapidfuzz==3.10.1
    # via quivr-core
regex==2024.9.11
    # via tiktoken
    # via transformers
requests==2.32.3
    # via cohere
    # via fasttext-langdetect
    # via huggingface-hub
    # via langchain
    # via langchain-community
    # via langfuse
    # via langsmith
    # via requests-toolbelt
    # via tiktoken
    # via transformers
requests-toolbelt==1.0.0
    # via langsmith
rich==13.9.2
    # via quivr-core
safetensors==0.4.5
    # via transformers
sentencepiece==0.2.0
    # via transformers
setuptools==75.6.0
    # via fasttext
six==1.16.0
    # via python-dateutil
sniffio==1.3.1
    # via anthropic
    # via anyio
    # via httpx
    # via openai
sqlalchemy==2.0.36
    # via langchain
    # via langchain-community
tabulate==0.9.0
    # via langchain-cohere
tenacity==8.5.0
    # via langchain
    # via langchain-community
    # via langchain-core
tiktoken==0.8.0
    # via langchain-openai
    # via quivr-core
tokenizers==0.20.1
    # via cohere
    # via langchain-mistralai
    # via transformers
tqdm==4.66.5
    # via huggingface-hub
    # via openai
    # via transformers
transformers==4.45.2
    # via quivr-core
types-pyyaml==6.0.12.20240917
    # via quivr-core
types-requests==2.32.0.20241016
    # via cohere
typing-extensions==4.12.2
    # via anthropic
    # via cohere
    # via huggingface-hub
    # via langchain-core
    # via openai
    # via pydantic
    # via pydantic-core
    # via sqlalchemy
    # via typing-inspect
typing-inspect==0.9.0
    # via dataclasses-json
tzdata==2024.2
    # via pandas
urllib3==2.2.3
    # via requests
    # via types-requests
wrapt==1.17.0
    # via langfuse
yarl==1.15.3
    # via aiohttp
