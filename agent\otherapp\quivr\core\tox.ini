[tox]
isolated_build = True
skipsdist = true
envlist =
  py311
  py311-base
  py311-unstructured
  py311-pdf


[testenv:py311]
allowlist_externals =
    poetry
commands_pre =
    poetry install --no-root --with test
commands =
    poetry run pytest tests/ -m "not base"  \
        --ignore=./tests/processor/epub \
        --ignore=./tests/processor/docx \
        --ignore=./tests/processor/odt \
        --ignore=./tests/processor/pdf  \
        --ignore=tests/processor/community

[testenv:py311-base]
allowlist_externals =
    poetry
commands_pre =
    poetry install --no-root --with test -E base
commands =
    poetry run pytest tests/ -m base  \
        --ignore=./tests/processor/epub \
        --ignore=./tests/processor/docx \
        --ignore=./tests/processor/odt \
        --ignore=./tests/processor/pdf \
        --ignore=tests/processor/community

[testenv:py311-unstructured]
allowlist_externals =
    poetry
commands_pre =
    poetry install --no-root \
                -E csv \
                -E md \
                -E ipynb \
                -E epub \
                -E odt \
                -E docx \
                -E pptx \
                -E xlsx \
                --with test
commands =
    poetry run pytest \
        tests/processor/epub \
        tests/processor/docx \
        tests/processor/docx \
        tests/processor/odt \
        tests/processor/community


[testenv:py311-pdf]
allowlist_externals =
    poetry
commands_pre =
    poetry install --no-root -E pdf --with test
commands =
    poetry run pytest tests/processor/pdf
