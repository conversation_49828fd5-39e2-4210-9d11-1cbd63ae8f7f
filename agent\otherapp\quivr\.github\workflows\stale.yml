name: "Close stale issues and PRs"
on:
  schedule:
    - cron: "0 */4 * * *"

permissions:
  contents: write # only for delete-branch option
  issues: write
  pull-requests: write

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@main
        with:
          exempt-assignees: true
          exempt-draft-pr: true
          days-before-stale: 90
          days-before-close: 5
          operations-per-run: 400
          exempt-milestones: true
          stale-issue-message: "Thanks for your contributions, we'll be closing this issue as it has gone stale. Feel free to reopen if you'd like to continue the discussion."
          stale-pr-message: "Thanks for your contributions, we'll be closing this PR as it has gone stale. Feel free to reopen if you'd like to continue the discussion."
