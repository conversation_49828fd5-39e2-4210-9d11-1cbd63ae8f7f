site_name: Quivr
extra_css:
  - css/style.css

markdown_extensions:
  - attr_list
  - admonition
  - pymdownx.details
  - pymdownx.superfences
  - md_in_html
  - toc:
      permalink: "#"

theme:
  custom_dir: overrides
  features:
    - navigation.instant
    - navigation.tabs
    - navigation.indexes
    - navigation.top
    - navigation.footer
    - toc.follow
    - content.code.copy
    - search.suggest
    - search.highlight
  name: material
  palette:
    - media: (prefers-color-scheme)
      toggle:
        icon: material/brightness-auto
        name: Switch to light mode
    - accent: purple
      media: "(prefers-color-scheme: light)"
      primary: white
      scheme: default
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - accent: purple
      media: "(prefers-color-scheme: dark)"
      primary: black
      scheme: slate
      toggle:
        icon: material/brightness-4
        name: Switch to system preference

plugins:
  - search
  - mkdocstrings:
      default_handler: python
      handlers:
        python:
          docstring_style: google
          options:
            show_source: false
            heading_level: 2
            separate_signature: true

nav:
  - Home:
      - index.md
      - quickstart.md
      - Brain:
          - brain/index.md
          - brain/brain.md
          - brain/chat.md
      - Storage:
          - storage/index.md
          - storage/base.md
          - storage/local_storage.md
      - Parsers:
          - parsers/index.md
          - parsers/megaparse.md
          - parsers/simple.md
      - Vector Stores:
          - vectorstores/index.md
          - vectorstores/faiss.md
          - vectorstores/pgvector.md
      - Workflows:
          - workflows/index.md
          - Examples:
              - workflows/examples/basic_ingestion.md
              - workflows/examples/basic_rag.md
              - workflows/examples/rag_with_web_search.md
      - Configuration:
          - config/index.md
          - config/config.md
          - config/base_config.md
      - Examples:
          - examples/index.md
          - examples/custom_storage.md
          - examples/chatbot.md
          - examples/chatbot_voice.md
          - examples/chatbot_voice_flask.md
  - Enterprise: https://docs.quivr.app/
