{"components": {"atoms": {"buttons": {"userButton": {"menu": {"settings": "সেটিংস", "settingsKey": "S", "APIKeys": "এপিআই কী", "logout": "লগআউট"}}}}, "molecules": {"newChatButton": {"newChat": "নতুন আড্ডা"}, "tasklist": {"TaskList": {"title": "🗒️ কার্য তালিকা", "loading": "লোড।।।", "error": "একটি ত্রুটি সংঘটিত হয়েছে"}}, "attachments": {"cancelUpload": "আপলোড বাতিল করুন", "removeAttachment": "সংযুক্তি সরান"}, "newChatDialog": {"createNewChat": "নতুন চ্যাট তৈরি করবেন?", "clearChat": "এটি বর্তমান বার্তাগুলি সাফ করবে এবং একটি নতুন চ্যাট শুরু করবে।", "cancel": "বাতিল", "confirm": "নিশ্চিত"}, "settingsModal": {"settings": "সেটিংস", "expandMessages": "বার্তাগুলি প্রসারিত করুন", "hideChainOfThought": "চিন্তার শৃঙ্খল লুকান", "darkMode": "ডার্ক মোড"}, "detailsButton": {"using": "ব্য<PERSON><PERSON><PERSON>র", "running": "চলমান", "took_one": "{{count}} পদক্ষেপ নিয়েছে", "took_other": "{{count}}টি পদক্ষেপ নিয়েছে"}, "auth": {"authLogin": {"title": "অ্যাপটি অ্যাক্সেস করতে লগইন করুন।", "form": {"email": "ই-মেইল ঠিকানা", "password": "পাসওয়ার্ড", "noAccount": "কোনও অ্যাকাউন্ট নেই?", "alreadyHaveAccount": "ইতিমধ্যে একটি অ্যাকাউন্ট আছে?", "signup": "সাইন আপ করো", "signin": "সাইন ইন করো", "or": "বা", "continue": "অবিরত", "forgotPassword": "পাসওয়ার্ড ভুলে গেছেন?", "passwordMustContain": "আপনার পাসওয়ার্ডে অবশ্যই থাকতে হবে:", "emailRequired": "ইমেল একটি প্রয়োজনীয় ক্ষেত্র", "passwordRequired": "পাসওয়ার্ড একটি আবশ্যক ক্ষেত্র"}, "error": {"default": "সাইন ইন করতে অক্ষম।", "signin": "একটি ভিন্ন অ্যাকাউন্ট দিয়ে সাইন ইন করার চেষ্টা করুন।", "oauthsignin": "একটি ভিন্ন অ্যাকাউন্ট দিয়ে সাইন ইন করার চেষ্টা করুন।", "redirect_uri_mismatch": "পুনঃনির্দেশিত URI OAUTH অ্যাপ কনফিগারেশনের সাথে মিলছে না।", "oauthcallbackerror": "একটি ভিন্ন অ্যাকাউন্ট দিয়ে সাইন ইন করার চেষ্টা করুন।", "oauthcreateaccount": "একটি ভিন্ন অ্যাকাউন্ট দিয়ে সাইন ইন করার চেষ্টা করুন।", "emailcreateaccount": "একটি ভিন্ন অ্যাকাউন্ট দিয়ে সাইন ইন করার চেষ্টা করুন।", "callback": "একটি ভিন্ন অ্যাকাউন্ট দিয়ে সাইন ইন করার চেষ্টা করুন।", "oauthaccountnotlinked": "আপনার পরিচয় নিশ্চিত করতে, আপনি মূলত যে অ্যাকাউন্টটি ব্যবহার করেছেন সেই একই অ্যাকাউন্ট দিয়ে সাইন ইন করুন।", "emailsignin": "ই-মেইলটি প্রেরণ করা যায়নি।", "emailverify": "অনুগ্রহ করে আপনার ইমেলটি যাচাই করুন, একটি নতুন ইমেল প্রেরণ করা হয়েছে।", "credentialssignin": "সাইন ইন ব্যর্থ হয়েছে। আপনার প্রদত্ত বিবরণগুলি সঠিক কিনা তা পরীক্ষা করুন।", "sessionrequired": "এই পৃষ্ঠাটি অ্যাক্সেস করতে দয়া করে সাইন ইন করুন।"}}, "authVerifyEmail": {"almostThere": "আপনি প্রায় সেখানে পৌঁছেছেন! আমরা একটি ইমেইল পাঠিয়েছি ", "verifyEmailLink": "আপনার সাইনআপ সম্পূর্ণ করতে দয়া করে সেই ইমেলের লিঙ্কটিতে ক্লিক করুন।", "didNotReceive": "ইমেইল খুঁজে পাচ্ছেন না?", "resendEmail": "ইমেইল পুনরায় পাঠান", "goBack": "ফিরে যাও", "emailSent": "ইমেল সফলভাবে পাঠানো হয়েছে।", "verifyEmail": "আপনার ইমেল ঠিকানা যাচাই করুন"}, "providerButton": {"continue": "{{provider}} দিয়ে চালিয়ে যান", "signup": "{{provider}} দিয়ে সাইন আপ করুন"}, "authResetPassword": {"newPasswordRequired": "নতুন পাসওয়ার্ড একটি আবশ্যক ক্ষেত্র", "passwordsMustMatch": "পাসওয়ার্ড অবশ্যই মিলতে হবে", "confirmPasswordRequired": "পাসওয়ার্ড নিশ্চিত করা একটি আবশ্যক ক্ষেত্র", "newPassword": "নতুন পাসওয়ার্ড", "confirmPassword": "পাসওয়ার্ড নিশ্চিত করুন", "resetPassword": "পাসওয়ার্ড রিসেট করুন"}, "authForgotPassword": {"email": "ই-মেইল ঠিকানা", "emailRequired": "ইমেল একটি প্রয়োজনীয় ক্ষেত্র", "emailSent": "আপনার পাসওয়ার্ডটি পুনরায় সেট করার নির্দেশাবলীর জন্য দয়া করে ইমেল ঠিকানা {{email}} পরীক্ষা করুন।", "enterEmail": "আপনার ইমেল ঠিকানা লিখুন এবং আমরা আপনাকে আপনার পাসওয়ার্ড পুনরায় সেট করতে নির্দেশাবলী পাঠাব।", "resendEmail": "ইমেইল পুনরায় পাঠান", "continue": "অবিরত", "goBack": "ফিরে যাও"}}}, "organisms": {"chat": {"history": {"index": {"showHistory": "ইতি<PERSON>া<PERSON> দেখান", "lastInputs": "সর্বশেষ ইনপুট", "noInputs": "এত ফাঁকা...", "loading": "লোড।।।"}}, "inputBox": {"input": {"placeholder": "এখানে আপনার বার্তা টাইপ করুন..."}, "speechButton": {"start": "রেকর্ডিং শুরু করুন", "stop": "রেকর্ডিং বন্ধ করুন"}, "SubmitButton": {"sendMessage": "বার্তা প্রেরণ করুন", "stopTask": "স্টপ টাস্ক"}, "UploadButton": {"attachFiles": "ফাইল সংযুক্ত করুন"}, "waterMark": {"text": "সঙ্গে নির্মিত"}}, "Messages": {"index": {"running": "চলমান", "executedSuccessfully": "সফলভাবে সম্পাদিত হয়েছে", "failed": "ব্যর্থ", "feedbackUpdated": "ফিডব্যাক আপডেট হয়েছে", "updating": "আধুনিকীকরণ"}}, "dropScreen": {"dropYourFilesHere": "আপনার ফাইলগুলি এখানে ফেলে দিন"}, "index": {"failedToUpload": "আপলোড করতে ব্যর্থ হয়েছে", "cancelledUploadOf": "এর আপলোড বাতিল", "couldNotReachServer": "সার্ভারে পৌঁছানো যায়নি", "continuingChat": "পূর্ববর্তী চ্যাট অবিরত রাখা"}, "settings": {"settingsPanel": "সেটিংস প্যানেল", "reset": "রিসেট", "cancel": "বাতিল", "confirm": "নিশ্চিত"}}, "threadHistory": {"sidebar": {"filters": {"FeedbackSelect": {"feedbackAll": "প্রতিক্রিয়া: সব", "feedbackPositive": "প্রতিক্রিয়া: ইতিবাচক", "feedbackNegative": "প্রতিক্রিয়া: নেতিবাচক"}, "SearchBar": {"search": "সন্ধান"}}, "DeleteThreadButton": {"confirmMessage": "এটি থ্রেডের পাশাপাশি এর বার্তা এবং উপাদানগুলিও মুছে ফেলবে।", "cancel": "বাতিল", "confirm": "নিশ্চিত", "deletingChat": "চ্যাট মোছা হচ্ছে", "chatDeleted": "চ্যাট মোছা হয়েছে"}, "index": {"pastChats": "অতীত চ্যাট"}, "ThreadList": {"empty": "খালি।।।", "today": "আজ", "yesterday": "গতকাল", "previous7days": "Previous 7 দিন", "previous30days": "পূর্ববর্তী 30 দিন"}, "TriggerButton": {"closeSidebar": "সাইডবার বন্ধ করুন", "openSidebar": "সাইডবার খুলুন"}}, "Thread": {"backToChat": "চ্যাটে ফিরে যান", "chatCreatedOn": "এই চ্যাটটি তৈরি করা হয়েছিল"}}, "header": {"chat": "<PERSON><PERSON><PERSON><PERSON>", "readme": "রিডমি"}}}, "hooks": {"useLLMProviders": {"failedToFetchProviders": "সরব<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>দের আনতে ব্যর্থ:"}}, "pages": {"Design": {}, "Env": {"savedSuccessfully": "সফলভাবে সংরক্ষণ করা হয়েছে", "requiredApiKeys": "আবশ্যক API কী", "requiredApiKeysInfo": "এই অ্যাপটি ব্যবহার করতে, নিম্নলিখিত API কীগুলির প্রয়োজন। কীগুলি আপনার ডিভাইসের স্থানীয় স্টোরেজে সঞ্চিত রয়েছে।"}, "Page": {"notPartOfProject": "আপনি এই প্রকল্পের অংশ নন।"}, "ResumeButton": {"resumeChat": "চ্যাট পুনরায় শুরু করুন"}}}